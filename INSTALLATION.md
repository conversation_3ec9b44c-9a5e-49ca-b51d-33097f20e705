# SmartShop WebApp Plugin - Installation Guide

This guide will walk you through the complete installation and setup process for the SmartShop WebApp Plugin.

## 📋 Prerequisites

Before installing SmartShop, ensure your WordPress site meets these requirements:

### Server Requirements
- **WordPress**: 5.0 or higher
- **PHP**: 7.4 or higher (8.0+ recommended)
- **MySQL**: 5.6 or higher (8.0+ recommended)
- **Memory Limit**: 128MB minimum (256MB recommended)
- **Max Upload Size**: 32MB minimum (for product images)

### WordPress Requirements
- Admin access to your WordPress site
- Ability to install and activate plugins
- Basic understanding of WordPress admin interface

## 🚀 Installation Methods

### Method 1: Manual Installation (Recommended)

1. **Download the Plugin**
   - Download all plugin files from the repository
   - Ensure the folder structure is intact

2. **Upload to WordPress**
   ```
   /wp-content/plugins/smartshop/
   ├── smartshop.php
   ├── admin/
   ├── assets/
   ├── includes/
   ├── public/
   ├── templates/
   └── README.md
   ```

3. **Set Permissions**
   - Ensure proper file permissions (644 for files, 755 for directories)
   - The uploads directory should be writable

4. **Activate the Plugin**
   - Go to WordPress Admin → Plugins
   - Find "SmartShop WebApp Plugin"
   - Click "Activate"

### Method 2: ZIP Upload

1. **Create ZIP File**
   - Compress the entire `smartshop` folder into a ZIP file
   - Ensure the main plugin file `smartshop.php` is in the root of the ZIP

2. **Upload via WordPress Admin**
   - Go to WordPress Admin → Plugins → Add New
   - Click "Upload Plugin"
   - Choose your ZIP file and click "Install Now"
   - Click "Activate Plugin"

## ⚙️ Initial Configuration

### Step 1: Database Setup

The plugin automatically creates necessary database tables upon activation. If you encounter issues:

1. **Check Database Permissions**
   - Ensure WordPress can create tables
   - Verify database user has CREATE and ALTER privileges

2. **Manual Table Creation** (if needed)
   - Go to SmartShop → Tools
   - Check "System Information" for table status
   - Contact support if tables are missing

### Step 2: Basic Settings

1. **Access Settings**
   - Navigate to SmartShop → Settings
   - Configure basic store information

2. **Store Information**
   ```
   Shop Name: Your Store Name
   Logo: Upload your store logo
   Primary Color: #3b82f6 (or your brand color)
   Secondary Color: #1e40af
   Currency: USD (or your local currency)
   Currency Symbol: $
   ```

3. **Payment Methods**
   - Enable Cash on Delivery (COD)
   - Configure manual payment instructions
   - Set up payment gateway integrations (if available)

### Step 3: Create Categories

1. **Navigate to Categories**
   - Go to SmartShop → Categories
   - The plugin creates default categories automatically

2. **Add Custom Categories**
   ```
   Category Name: Electronics
   Slug: electronics
   Description: Electronic devices and gadgets
   Parent Category: None (for main categories)
   ```

3. **Category Hierarchy**
   - Create main categories first
   - Add subcategories as needed
   - Use descriptive names and SEO-friendly slugs

### Step 4: Add Your First Product

1. **Go to Add Product**
   - Navigate to SmartShop → Add Product

2. **Product Information**
   ```
   Product Name: Sample Product
   Description: Detailed product description
   Short Description: Brief summary
   Price: 29.99
   Sale Price: 24.99 (optional)
   SKU: PROD-001
   Category: Select appropriate category
   ```

3. **Inventory Settings**
   ```
   Manage Stock: Yes/No
   Stock Quantity: 100
   Stock Status: In Stock
   ```

4. **Product Images**
   - Upload main product image
   - Add gallery images (optional)
   - Optimize images for web (recommended: 800x800px)

### Step 5: Configure Pages

The plugin creates virtual pages automatically:

- **Shop Page**: `/shop/` - Main product listing
- **Cart Page**: `/cart/` - Shopping cart
- **Checkout Page**: `/checkout/` - Order process
- **Account Page**: `/my-account/` - User account area

**Custom Page Setup** (optional):
1. Create WordPress pages for each section
2. Use shortcodes to display content:
   ```
   [smartshop_products limit="12" columns="4"]
   [smartshop_cart]
   [smartshop_checkout]
   [smartshop_account]
   ```

## 🎨 Theme Integration

### Basic Integration

1. **Check Theme Compatibility**
   - Test with your current theme
   - Ensure responsive design works properly

2. **Add Navigation Menu**
   ```php
   // Add to your theme's menu
   <a href="/shop/">Shop</a>
   <a href="/cart/">Cart (<span class="cart-count">0</span>)</a>
   ```

3. **Include Cart Icon**
   ```php
   // Add cart widget to header
   echo do_shortcode('[smartshop_cart_icon]');
   ```

### Advanced Integration

1. **Template Overrides**
   - Copy templates from plugin to theme
   - Customize as needed
   ```
   your-theme/
     smartshop/
       shop.php
       single-product.php
       cart.php
       checkout.php
   ```

2. **Custom Styling**
   ```css
   /* Add to your theme's CSS */
   :root {
     --smartshop-primary-color: #your-color;
     --smartshop-secondary-color: #your-color;
   }
   ```

## 🔧 Advanced Configuration

### Email Settings

1. **SMTP Configuration** (recommended)
   - Install SMTP plugin (WP Mail SMTP)
   - Configure email delivery
   - Test order confirmation emails

2. **Email Templates**
   - Customize email templates in `/templates/emails/`
   - Test with sample orders

### Performance Optimization

1. **Caching**
   - Install caching plugin (WP Rocket, W3 Total Cache)
   - Configure object caching if available
   - Enable browser caching

2. **Image Optimization**
   - Install image optimization plugin
   - Optimize existing product images
   - Set up automatic optimization

3. **Database Optimization**
   - Regular database cleanup
   - Optimize database tables
   - Monitor database size

### Security Configuration

1. **SSL Certificate**
   - Ensure HTTPS is enabled
   - Force SSL for checkout pages
   - Update WordPress URLs to HTTPS

2. **Security Plugins**
   - Install security plugin (Wordfence, Sucuri)
   - Configure firewall rules
   - Enable login protection

## 🧪 Testing Your Installation

### Functional Testing

1. **Product Management**
   - [ ] Add a test product
   - [ ] Upload product images
   - [ ] Set inventory levels
   - [ ] Assign categories

2. **Shopping Cart**
   - [ ] Add products to cart
   - [ ] Update quantities
   - [ ] Remove items
   - [ ] Cart persistence across sessions

3. **Checkout Process**
   - [ ] Complete test order
   - [ ] Test payment methods
   - [ ] Verify order confirmation
   - [ ] Check email notifications

4. **Admin Functions**
   - [ ] View orders in admin
   - [ ] Update order status
   - [ ] Export data
   - [ ] Check system information

### Performance Testing

1. **Page Load Speed**
   - Test shop page load time
   - Optimize if needed
   - Use tools like GTmetrix or PageSpeed Insights

2. **Mobile Responsiveness**
   - Test on various devices
   - Check touch interactions
   - Verify mobile checkout process

## 🐛 Troubleshooting

### Common Issues

**Plugin Won't Activate**
```
Solution:
1. Check PHP version (7.4+ required)
2. Increase memory limit in wp-config.php:
   ini_set('memory_limit', '256M');
3. Check for plugin conflicts
4. Review error logs
```

**Database Tables Not Created**
```
Solution:
1. Check database permissions
2. Manually deactivate and reactivate plugin
3. Check wp-config.php database settings
4. Contact hosting provider if needed
```

**Products Not Displaying**
```
Solution:
1. Ensure products are published
2. Check category assignments
3. Verify shortcode syntax
4. Clear any caching
```

**Cart Not Working**
```
Solution:
1. Check JavaScript console for errors
2. Ensure jQuery is loaded
3. Verify AJAX URL configuration
4. Test with default theme
```

### Debug Mode

Enable WordPress debug mode for troubleshooting:

```php
// Add to wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

Check debug logs in `/wp-content/debug.log`

## 📞 Getting Help

### Documentation
- Plugin documentation in `/docs/` directory
- WordPress Codex for general WordPress help
- PHP documentation for custom development

### Support Channels
- GitHub Issues for bug reports
- Community forums for general questions
- Professional support for custom development

### Before Requesting Support

Please provide:
1. WordPress version
2. PHP version
3. Plugin version
4. Active theme name
5. List of active plugins
6. Error messages (if any)
7. Steps to reproduce the issue

## 🔄 Updates and Maintenance

### Regular Maintenance
- Keep WordPress core updated
- Update plugins regularly
- Monitor security advisories
- Backup site regularly

### Plugin Updates
- Check for plugin updates
- Test updates on staging site first
- Review changelog before updating
- Backup before major updates

---

**Congratulations!** 🎉 Your SmartShop WebApp Plugin is now installed and ready to transform your WordPress site into a powerful eCommerce platform!
