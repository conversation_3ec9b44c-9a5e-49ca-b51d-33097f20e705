# SmartShop WebApp Plugin

A modern, multipurpose WordPress plugin that transforms any WordPress site into a dynamic eCommerce web app without requiring WooCommerce. Perfect for grocery, fashion, electronics, beauty, and other product niches.

## 🚀 Features

### Core Functionality
- **Custom Product Management** - Add, edit, and manage products with images, categories, and inventory
- **Shopping Cart System** - AJAX-powered cart with session persistence
- **Checkout Process** - Streamlined checkout with multiple payment options
- **Order Management** - Complete order tracking and status management
- **Category System** - Organize products with hierarchical categories

### Customization Options
- **Brand Customization** - Easy logo, colors, and theme customization
- **Multiple Layouts** - Grid, list, and card-style product displays
- **Responsive Design** - Mobile-first, fully responsive interface
- **Color Schemes** - Customizable primary, secondary, and accent colors

### Payment & Shipping
- **Cash on Delivery (COD)** - Built-in COD support
- **Manual Payment** - Bank transfer and other manual payment methods
- **Payment Gateway Ready** - Extensible for <PERSON>e, PayPal, and other gateways
- **Flexible Shipping** - Optional shipping cost calculation

### User Experience
- **Guest Checkout** - Allow purchases without registration
- **User Accounts** - Optional user registration and order history
- **Search & Filters** - Advanced product search and filtering
- **AJAX Cart** - Real-time cart updates without page refresh

### Admin Features
- **Dashboard Analytics** - Sales statistics and inventory alerts
- **Bulk Operations** - Import/export products and orders
- **Email Notifications** - Automated order confirmation emails
- **Inventory Management** - Stock tracking and low stock alerts

### Developer Features
- **Shortcodes** - Display products anywhere with shortcodes
- **Gutenberg Blocks** - Modern block editor support
- **Multisite Compatible** - Works with WordPress multisite
- **Translation Ready** - Full internationalization support
- **RTL Support** - Right-to-left language compatibility

## 📋 Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher
- Modern web browser with JavaScript enabled

## 🛠 Installation

### Manual Installation

1. **Download the Plugin**
   - Download the plugin files or clone this repository
   - Ensure all files are in a folder named `smartshop`

2. **Upload to WordPress**
   ```
   wp-content/plugins/smartshop/
   ```

3. **Activate the Plugin**
   - Go to WordPress Admin → Plugins
   - Find "SmartShop WebApp Plugin" and click "Activate"

4. **Initial Setup**
   - The plugin will automatically create necessary database tables
   - Visit SmartShop → Settings to configure your store

### Via WordPress Admin

1. Upload the plugin ZIP file through WordPress Admin → Plugins → Add New → Upload Plugin
2. Activate the plugin
3. Complete the initial setup

## ⚙️ Configuration

### Basic Setup

1. **Store Settings**
   - Go to SmartShop → Settings
   - Configure your store name, logo, and colors
   - Set up currency and payment methods

2. **Add Categories**
   - Navigate to SmartShop → Categories
   - Create product categories for organization

3. **Add Products**
   - Go to SmartShop → Add Product
   - Add your first products with images and details

4. **Configure Pages**
   - The plugin automatically creates virtual pages:
     - `/shop/` - Main shop page
     - `/cart/` - Shopping cart
     - `/checkout/` - Checkout process
     - `/my-account/` - User account area

### Advanced Configuration

#### Payment Methods
```php
// Enable/disable payment methods in Settings
- Cash on Delivery (COD)
- Manual Payment Instructions
- Future: Stripe, PayPal integration
```

#### Email Settings
```php
// Configure in SmartShop → Settings → Email
- Admin notification email
- Order confirmation emails
- Custom email templates
```

#### Inventory Management
```php
// Stock management options
- Enable/disable stock tracking
- Low stock threshold alerts
- Out of stock visibility
```

## 🎨 Customization

### Theme Integration

The plugin uses CSS variables for easy customization:

```css
:root {
  --smartshop-primary-color: #3b82f6;
  --smartshop-secondary-color: #1e40af;
  --smartshop-accent-color: #f59e0b;
}
```

### Shortcodes

Display products anywhere on your site:

```php
// Product grid
[smartshop_products limit="12" columns="4"]

// Featured products
[smartshop_featured_products limit="8"]

// Product categories
[smartshop_categories columns="3"]

// Shopping cart
[smartshop_cart]

// Checkout form
[smartshop_checkout]

// Search form
[smartshop_search]
```

### Template Overrides

Override plugin templates in your theme:

```
your-theme/
  smartshop/
    shop.php
    single-product.php
    cart.php
    checkout.php
```

## 🔧 Development

### Hooks and Filters

The plugin provides numerous hooks for customization:

```php
// Product hooks
add_action('smartshop_before_product_grid', 'your_function');
add_action('smartshop_after_product_card', 'your_function');
add_filter('smartshop_product_price', 'your_function');

// Cart hooks
add_action('smartshop_cart_item_added', 'your_function');
add_filter('smartshop_cart_total', 'your_function');

// Order hooks
add_action('smartshop_order_created', 'your_function');
add_action('smartshop_order_status_changed', 'your_function');
```

### Database Schema

The plugin creates these tables:
- `wp_smartshop_products` - Product data
- `wp_smartshop_categories` - Product categories
- `wp_smartshop_orders` - Order information
- `wp_smartshop_order_items` - Order line items
- `wp_smartshop_cart` - Persistent cart data
- `wp_smartshop_settings` - Plugin settings

### API Endpoints

AJAX endpoints for frontend functionality:
- `smartshop_add_to_cart`
- `smartshop_update_cart`
- `smartshop_remove_from_cart`
- `smartshop_get_products`
- `smartshop_create_order`

## 🌐 Internationalization

The plugin is translation-ready with text domain `smartshop`.

### Supported Languages
- English (default)
- Ready for translation to any language
- RTL language support included

### Adding Translations
1. Use tools like Poedit to create translation files
2. Place `.po` and `.mo` files in `/languages/` directory
3. Files should be named `smartshop-{locale}.po/mo`

## 🔒 Security

### Data Sanitization
- All user inputs are sanitized and validated
- SQL injection protection with prepared statements
- XSS prevention with proper escaping

### Permissions
- Admin functions require `manage_options` capability
- AJAX requests use WordPress nonces
- File uploads restricted to images only

### Best Practices
- Regular security updates
- Secure coding standards
- WordPress coding standards compliance

## 📊 Performance

### Optimization Features
- Efficient database queries with proper indexing
- AJAX loading for better user experience
- Image optimization recommendations
- Caching-friendly architecture

### Recommended Optimizations
- Use a caching plugin (WP Rocket, W3 Total Cache)
- Optimize images before upload
- Use a CDN for static assets
- Regular database optimization

## 🐛 Troubleshooting

### Common Issues

**Plugin activation fails**
- Check PHP version (7.4+ required)
- Verify database permissions
- Check for plugin conflicts

**Products not displaying**
- Ensure products are published
- Check category assignments
- Verify shortcode syntax

**Cart not working**
- Check JavaScript console for errors
- Verify AJAX URL configuration
- Ensure sessions are working

**Orders not saving**
- Check database table creation
- Verify form field validation
- Check email configuration

### Debug Mode

Enable WordPress debug mode for troubleshooting:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## 📞 Support

### Documentation
- Plugin documentation available in `/docs/` directory
- Inline code documentation
- WordPress Codex compatibility

### Community
- GitHub Issues for bug reports
- Feature requests welcome
- Community contributions encouraged

## 📄 License

This plugin is licensed under the GPL v2 or later.

```
This program is free software; you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation; either version 2 of the License, or
(at your option) any later version.
```

## 🤝 Contributing

Contributions are welcome! Please:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

### Development Setup
```bash
git clone https://github.com/your-repo/smartshop-plugin.git
cd smartshop-plugin
# Set up local WordPress development environment
# Install dependencies if any
```

## 🔄 Changelog

### Version 1.0.0
- Initial release
- Core eCommerce functionality
- Product and order management
- Shopping cart and checkout
- Admin dashboard
- Responsive design
- Multiple payment methods
- Shortcode support

---

**SmartShop WebApp Plugin** - Transform your WordPress site into a powerful eCommerce platform! 🛒✨
