<?php
/**
 * SmartShop Admin Orders Class
 * 
 * Handles order management in admin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Admin_Orders {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('admin_init', array($this, 'handle_actions'));
    }
    
    /**
     * Handle admin actions
     */
    public function handle_actions() {
        if (isset($_POST['smartshop_action'])) {
            $this->process_form_submission();
        }
    }
    
    /**
     * Orders page
     */
    public static function orders_page() {
        $status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
        $current_page = max(1, intval($_GET['paged'] ?? 1));
        $per_page = 20;
        $offset = ($current_page - 1) * $per_page;
        
        $args = array(
            'limit' => $per_page,
            'offset' => $offset
        );
        
        if ($status_filter) {
            $args['status'] = $status_filter;
        }
        
        $orders = SmartShop_Order::get_orders($args);
        $total_orders = SmartShop_Order::get_order_count($args);
        $total_pages = ceil($total_orders / $per_page);
        
        include SMARTSHOP_PLUGIN_PATH . 'admin/views/orders.php';
    }
    
    /**
     * Process form submission
     */
    private function process_form_submission() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        check_admin_referer('smartshop_admin_action');
        
        $action = sanitize_text_field($_POST['smartshop_action']);
        
        switch ($action) {
            case 'update_order_status':
                $this->handle_update_order_status();
                break;
            case 'delete_order':
                $this->handle_delete_order();
                break;
        }
    }
    
    /**
     * Handle update order status
     */
    private function handle_update_order_status() {
        $order_id = intval($_POST['order_id']);
        $status = sanitize_text_field($_POST['order_status']);
        
        $success = SmartShop_Order::update_order_status($order_id, $status);
        
        if ($success) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('Order status updated successfully!', 'smartshop') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Failed to update order status.', 'smartshop') . '</p></div>';
            });
        }
    }
    
    /**
     * Handle delete order
     */
    private function handle_delete_order() {
        $order_id = intval($_POST['order_id']);
        
        global $wpdb;
        $orders_table = SmartShop_Database::get_table_name('orders');
        $order_items_table = SmartShop_Database::get_table_name('order_items');
        
        // Delete order items first
        $wpdb->delete($order_items_table, array('order_id' => $order_id), array('%d'));
        
        // Delete order
        $result = $wpdb->delete($orders_table, array('id' => $order_id), array('%d'));
        
        if ($result !== false) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('Order deleted successfully!', 'smartshop') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Failed to delete order.', 'smartshop') . '</p></div>';
            });
        }
    }
    
    /**
     * Get order statuses
     */
    public static function get_order_statuses() {
        return array(
            'pending' => __('Pending', 'smartshop'),
            'processing' => __('Processing', 'smartshop'),
            'completed' => __('Completed', 'smartshop'),
            'cancelled' => __('Cancelled', 'smartshop')
        );
    }
    
    /**
     * Get payment methods
     */
    public static function get_payment_methods() {
        return array(
            'cod' => __('Cash on Delivery', 'smartshop'),
            'manual_payment' => __('Manual Payment', 'smartshop'),
            'stripe' => __('Stripe', 'smartshop'),
            'paypal' => __('PayPal', 'smartshop')
        );
    }
}
