<?php
/**
 * SmartShop Admin Products Class
 * 
 * Handles product management in admin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Admin_Products {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('admin_init', array($this, 'handle_actions'));
    }
    
    /**
     * Handle admin actions
     */
    public function handle_actions() {
        if (isset($_POST['smartshop_action'])) {
            $this->process_form_submission();
        }
    }
    
    /**
     * Products page
     */
    public static function products_page() {
        $products = SmartShop_Product::get_products(array('limit' => 50));
        include SMARTSHOP_PLUGIN_PATH . 'admin/views/products.php';
    }
    
    /**
     * Add product page
     */
    public static function add_product_page() {
        $categories = self::get_categories();
        include SMARTSHOP_PLUGIN_PATH . 'admin/views/add-product.php';
    }
    
    /**
     * Categories page
     */
    public static function categories_page() {
        $categories = self::get_categories();
        include SMARTSHOP_PLUGIN_PATH . 'admin/views/categories.php';
    }
    
    /**
     * Process form submission
     */
    private function process_form_submission() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        check_admin_referer('smartshop_admin_action');
        
        $action = sanitize_text_field($_POST['smartshop_action']);
        
        switch ($action) {
            case 'add_product':
                $this->handle_add_product();
                break;
            case 'edit_product':
                $this->handle_edit_product();
                break;
            case 'delete_product':
                $this->handle_delete_product();
                break;
            case 'add_category':
                $this->handle_add_category();
                break;
        }
    }
    
    /**
     * Handle add product
     */
    private function handle_add_product() {
        $product_data = array(
            'name' => sanitize_text_field($_POST['product_name']),
            'description' => wp_kses_post($_POST['product_description']),
            'short_description' => sanitize_textarea_field($_POST['product_short_description']),
            'price' => floatval($_POST['product_price']),
            'sale_price' => !empty($_POST['product_sale_price']) ? floatval($_POST['product_sale_price']) : null,
            'sku' => sanitize_text_field($_POST['product_sku']),
            'category_id' => intval($_POST['product_category']),
            'image_url' => esc_url_raw($_POST['product_image']),
            'stock_quantity' => !empty($_POST['stock_quantity']) ? intval($_POST['stock_quantity']) : null,
            'manage_stock' => isset($_POST['manage_stock']) ? 1 : 0,
            'featured' => isset($_POST['featured']) ? 1 : 0,
            'status' => 'publish'
        );
        
        $product_id = SmartShop_Product::create_product($product_data);
        
        if ($product_id) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('Product added successfully!', 'smartshop') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Failed to add product.', 'smartshop') . '</p></div>';
            });
        }
    }
    
    /**
     * Handle edit product
     */
    private function handle_edit_product() {
        $product_id = intval($_POST['product_id']);
        
        $product_data = array(
            'name' => sanitize_text_field($_POST['product_name']),
            'description' => wp_kses_post($_POST['product_description']),
            'short_description' => sanitize_textarea_field($_POST['product_short_description']),
            'price' => floatval($_POST['product_price']),
            'sale_price' => !empty($_POST['product_sale_price']) ? floatval($_POST['product_sale_price']) : null,
            'sku' => sanitize_text_field($_POST['product_sku']),
            'category_id' => intval($_POST['product_category']),
            'image_url' => esc_url_raw($_POST['product_image']),
            'stock_quantity' => !empty($_POST['stock_quantity']) ? intval($_POST['stock_quantity']) : null,
            'manage_stock' => isset($_POST['manage_stock']) ? 1 : 0,
            'featured' => isset($_POST['featured']) ? 1 : 0
        );
        
        $success = SmartShop_Product::update_product($product_id, $product_data);
        
        if ($success) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('Product updated successfully!', 'smartshop') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Failed to update product.', 'smartshop') . '</p></div>';
            });
        }
    }
    
    /**
     * Handle delete product
     */
    private function handle_delete_product() {
        $product_id = intval($_POST['product_id']);
        
        $success = SmartShop_Product::delete_product($product_id);
        
        if ($success) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('Product deleted successfully!', 'smartshop') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Failed to delete product.', 'smartshop') . '</p></div>';
            });
        }
    }
    
    /**
     * Handle add category
     */
    private function handle_add_category() {
        global $wpdb;
        
        $categories_table = SmartShop_Database::get_table_name('categories');
        
        $category_data = array(
            'name' => sanitize_text_field($_POST['category_name']),
            'slug' => sanitize_title($_POST['category_slug'] ?: $_POST['category_name']),
            'description' => sanitize_textarea_field($_POST['category_description']),
            'parent_id' => intval($_POST['parent_category']),
            'image_url' => esc_url_raw($_POST['category_image']),
            'status' => 'active'
        );
        
        $result = $wpdb->insert($categories_table, $category_data);
        
        if ($result !== false) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('Category added successfully!', 'smartshop') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Failed to add category.', 'smartshop') . '</p></div>';
            });
        }
    }
    
    /**
     * Get categories
     */
    private static function get_categories() {
        global $wpdb;
        
        $categories_table = SmartShop_Database::get_table_name('categories');
        
        return $wpdb->get_results("SELECT * FROM $categories_table WHERE status = 'active' ORDER BY name ASC");
    }
}
