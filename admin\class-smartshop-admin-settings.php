<?php
/**
 * SmartShop Admin Settings Class
 * 
 * Handles plugin settings in admin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Admin_Settings {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('admin_init', array($this, 'handle_actions'));
    }
    
    /**
     * Handle admin actions
     */
    public function handle_actions() {
        if (isset($_POST['smartshop_action']) && $_POST['smartshop_action'] === 'save_settings') {
            $this->save_settings();
        }
    }
    
    /**
     * Settings page
     */
    public static function settings_page() {
        $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'general';
        include SMARTSHOP_PLUGIN_PATH . 'admin/views/settings.php';
    }
    
    /**
     * Save settings
     */
    private function save_settings() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        check_admin_referer('smartshop_settings_action');
        
        $tab = sanitize_text_field($_POST['tab']);
        
        switch ($tab) {
            case 'general':
                $this->save_general_settings();
                break;
            case 'payment':
                $this->save_payment_settings();
                break;
            case 'email':
                $this->save_email_settings();
                break;
            case 'appearance':
                $this->save_appearance_settings();
                break;
        }
        
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', 'smartshop') . '</p></div>';
        });
    }
    
    /**
     * Save general settings
     */
    private function save_general_settings() {
        $settings = array(
            'shop_name' => sanitize_text_field($_POST['shop_name']),
            'shop_logo' => esc_url_raw($_POST['shop_logo']),
            'currency' => sanitize_text_field($_POST['currency']),
            'currency_symbol' => sanitize_text_field($_POST['currency_symbol']),
            'currency_position' => sanitize_text_field($_POST['currency_position']),
            'products_per_page' => intval($_POST['products_per_page']),
            'enable_search' => isset($_POST['enable_search']) ? 1 : 0,
            'enable_filters' => isset($_POST['enable_filters']) ? 1 : 0,
            'enable_user_registration' => isset($_POST['enable_user_registration']) ? 1 : 0,
            'enable_guest_checkout' => isset($_POST['enable_guest_checkout']) ? 1 : 0
        );
        
        SmartShop_Settings::update_options($settings);
    }
    
    /**
     * Save payment settings
     */
    private function save_payment_settings() {
        $settings = array(
            'enable_cod' => isset($_POST['enable_cod']) ? 1 : 0,
            'enable_manual_payment' => isset($_POST['enable_manual_payment']) ? 1 : 0,
            'cod_instructions' => sanitize_textarea_field($_POST['cod_instructions']),
            'manual_payment_instructions' => sanitize_textarea_field($_POST['manual_payment_instructions']),
            'enable_shipping' => isset($_POST['enable_shipping']) ? 1 : 0,
            'shipping_cost' => floatval($_POST['shipping_cost']),
            'free_shipping_threshold' => floatval($_POST['free_shipping_threshold']),
            'enable_tax' => isset($_POST['enable_tax']) ? 1 : 0,
            'tax_rate' => floatval($_POST['tax_rate'])
        );
        
        SmartShop_Settings::update_options($settings);
    }
    
    /**
     * Save email settings
     */
    private function save_email_settings() {
        $settings = array(
            'admin_email' => sanitize_email($_POST['admin_email']),
            'from_email' => sanitize_email($_POST['from_email']),
            'from_name' => sanitize_text_field($_POST['from_name']),
            'enable_order_emails' => isset($_POST['enable_order_emails']) ? 1 : 0
        );
        
        SmartShop_Settings::update_options($settings);
    }
    
    /**
     * Save appearance settings
     */
    private function save_appearance_settings() {
        $settings = array(
            'primary_color' => sanitize_hex_color($_POST['primary_color']),
            'secondary_color' => sanitize_hex_color($_POST['secondary_color']),
            'accent_color' => sanitize_hex_color($_POST['accent_color']),
            'layout_style' => sanitize_text_field($_POST['layout_style']),
            'grid_columns_desktop' => intval($_POST['grid_columns_desktop']),
            'grid_columns_tablet' => intval($_POST['grid_columns_tablet']),
            'grid_columns_mobile' => intval($_POST['grid_columns_mobile']),
            'show_breadcrumbs' => isset($_POST['show_breadcrumbs']) ? 1 : 0,
            'show_product_count' => isset($_POST['show_product_count']) ? 1 : 0,
            'show_sorting' => isset($_POST['show_sorting']) ? 1 : 0
        );
        
        SmartShop_Settings::update_options($settings);
    }
    
    /**
     * Get setting tabs
     */
    public static function get_setting_tabs() {
        return array(
            'general' => __('General', 'smartshop'),
            'payment' => __('Payment', 'smartshop'),
            'email' => __('Email', 'smartshop'),
            'appearance' => __('Appearance', 'smartshop')
        );
    }
    
    /**
     * Get currencies
     */
    public static function get_currencies() {
        return array(
            'USD' => array('name' => 'US Dollar', 'symbol' => '$'),
            'EUR' => array('name' => 'Euro', 'symbol' => '€'),
            'GBP' => array('name' => 'British Pound', 'symbol' => '£'),
            'BDT' => array('name' => 'Bangladeshi Taka', 'symbol' => '৳'),
            'INR' => array('name' => 'Indian Rupee', 'symbol' => '₹'),
            'CAD' => array('name' => 'Canadian Dollar', 'symbol' => 'C$'),
            'AUD' => array('name' => 'Australian Dollar', 'symbol' => 'A$'),
            'JPY' => array('name' => 'Japanese Yen', 'symbol' => '¥')
        );
    }
}
