<?php
/**
 * SmartShop Admin Class
 * 
 * Handles admin interface and functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Admin {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('admin_notices', array($this, 'admin_notices'));
        add_filter('plugin_action_links_' . SMARTSHOP_PLUGIN_BASENAME, array($this, 'plugin_action_links'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Main menu
        add_menu_page(
            __('SmartShop', 'smartshop'),
            __('SmartShop', 'smartshop'),
            'manage_options',
            'smartshop',
            array($this, 'dashboard_page'),
            'dashicons-store',
            30
        );
        
        // Dashboard submenu
        add_submenu_page(
            'smartshop',
            __('Dashboard', 'smartshop'),
            __('Dashboard', 'smartshop'),
            'manage_options',
            'smartshop',
            array($this, 'dashboard_page')
        );
        
        // Products submenu
        add_submenu_page(
            'smartshop',
            __('Products', 'smartshop'),
            __('Products', 'smartshop'),
            'manage_options',
            'smartshop-products',
            array('SmartShop_Admin_Products', 'products_page')
        );
        
        // Add Product submenu
        add_submenu_page(
            'smartshop',
            __('Add Product', 'smartshop'),
            __('Add Product', 'smartshop'),
            'manage_options',
            'smartshop-add-product',
            array('SmartShop_Admin_Products', 'add_product_page')
        );
        
        // Categories submenu
        add_submenu_page(
            'smartshop',
            __('Categories', 'smartshop'),
            __('Categories', 'smartshop'),
            'manage_options',
            'smartshop-categories',
            array('SmartShop_Admin_Products', 'categories_page')
        );
        
        // Orders submenu
        add_submenu_page(
            'smartshop',
            __('Orders', 'smartshop'),
            __('Orders', 'smartshop'),
            'manage_options',
            'smartshop-orders',
            array('SmartShop_Admin_Orders', 'orders_page')
        );
        
        // Settings submenu
        add_submenu_page(
            'smartshop',
            __('Settings', 'smartshop'),
            __('Settings', 'smartshop'),
            'manage_options',
            'smartshop-settings',
            array('SmartShop_Admin_Settings', 'settings_page')
        );
        
        // Tools submenu
        add_submenu_page(
            'smartshop',
            __('Tools', 'smartshop'),
            __('Tools', 'smartshop'),
            'manage_options',
            'smartshop-tools',
            array($this, 'tools_page')
        );
    }
    
    /**
     * Admin initialization
     */
    public function admin_init() {
        // Check if tables exist and create if needed
        if (!SmartShop_Database::tables_exist()) {
            SmartShop_Database::create_tables();
        }
        
        // Handle plugin activation notice
        if (get_option('smartshop_activated')) {
            add_action('admin_notices', array($this, 'activation_notice'));
            delete_option('smartshop_activated');
        }
    }
    
    /**
     * Dashboard page
     */
    public function dashboard_page() {
        // Get dashboard statistics
        $stats = $this->get_dashboard_stats();
        
        include SMARTSHOP_PLUGIN_PATH . 'admin/views/dashboard.php';
    }
    
    /**
     * Tools page
     */
    public function tools_page() {
        // Handle form submissions
        if (isset($_POST['smartshop_action'])) {
            $this->handle_tools_actions();
        }
        
        include SMARTSHOP_PLUGIN_PATH . 'admin/views/tools.php';
    }
    
    /**
     * Get dashboard statistics
     */
    private function get_dashboard_stats() {
        global $wpdb;
        
        $products_table = SmartShop_Database::get_table_name('products');
        $orders_table = SmartShop_Database::get_table_name('orders');
        $categories_table = SmartShop_Database::get_table_name('categories');
        
        $stats = array();
        
        // Total products
        $stats['total_products'] = $wpdb->get_var("SELECT COUNT(*) FROM $products_table WHERE status = 'publish'");
        
        // Total categories
        $stats['total_categories'] = $wpdb->get_var("SELECT COUNT(*) FROM $categories_table WHERE status = 'active'");
        
        // Total orders
        $stats['total_orders'] = $wpdb->get_var("SELECT COUNT(*) FROM $orders_table");
        
        // Pending orders
        $stats['pending_orders'] = $wpdb->get_var("SELECT COUNT(*) FROM $orders_table WHERE status = 'pending'");
        
        // Total sales
        $stats['total_sales'] = $wpdb->get_var("SELECT SUM(total) FROM $orders_table WHERE status IN ('completed', 'processing')") ?: 0;
        
        // This month sales
        $stats['month_sales'] = $wpdb->get_var("SELECT SUM(total) FROM $orders_table WHERE status IN ('completed', 'processing') AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())") ?: 0;
        
        // Low stock products
        $stats['low_stock_products'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $products_table WHERE manage_stock = 1 AND stock_quantity <= %d AND status = 'publish'",
            SmartShop_Settings::get_option('low_stock_threshold', 5)
        ));
        
        // Out of stock products
        $stats['out_of_stock_products'] = $wpdb->get_var("SELECT COUNT(*) FROM $products_table WHERE stock_status = 'outofstock' AND status = 'publish'");
        
        // Recent orders
        $stats['recent_orders'] = $wpdb->get_results("SELECT * FROM $orders_table ORDER BY created_at DESC LIMIT 5");
        
        // Top selling products (placeholder)
        $stats['top_products'] = array();
        
        return $stats;
    }
    
    /**
     * Handle tools actions
     */
    private function handle_tools_actions() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        check_admin_referer('smartshop_tools_action');
        
        $action = sanitize_text_field($_POST['smartshop_action']);
        
        switch ($action) {
            case 'export_products':
                $this->export_products();
                break;
                
            case 'export_orders':
                $this->export_orders();
                break;
                
            case 'import_products':
                $this->import_products();
                break;
                
            case 'clear_cache':
                $this->clear_cache();
                break;
                
            case 'reset_settings':
                $this->reset_settings();
                break;
        }
    }
    
    /**
     * Export products to CSV
     */
    private function export_products() {
        $products = SmartShop_Product::get_products(array('limit' => -1));
        
        if (empty($products)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('No products found to export.', 'smartshop') . '</p></div>';
            });
            return;
        }
        
        $filename = 'smartshop-products-' . date('Y-m-d-H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, array(
            'ID', 'Name', 'Slug', 'Description', 'Price', 'Sale Price', 'SKU', 
            'Stock Quantity', 'Stock Status', 'Category', 'Image URL', 'Status', 'Featured'
        ));
        
        // CSV data
        foreach ($products as $product) {
            fputcsv($output, array(
                $product->id,
                $product->name,
                $product->slug,
                $product->description,
                $product->price,
                $product->sale_price,
                $product->sku,
                $product->stock_quantity,
                $product->stock_status,
                $product->category_name,
                $product->image_url,
                $product->status,
                $product->featured ? 'Yes' : 'No'
            ));
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Export orders to CSV
     */
    private function export_orders() {
        $orders = SmartShop_Order::get_orders(array('limit' => -1));
        
        if (empty($orders)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('No orders found to export.', 'smartshop') . '</p></div>';
            });
            return;
        }
        
        $filename = 'smartshop-orders-' . date('Y-m-d-H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, array(
            'Order Number', 'Date', 'Status', 'Total', 'Payment Method', 
            'Customer Name', 'Customer Email', 'Customer Phone'
        ));
        
        // CSV data
        foreach ($orders as $order) {
            fputcsv($output, array(
                $order->order_number,
                $order->created_at,
                $order->status,
                $order->total,
                $order->payment_method,
                $order->billing_data['first_name'] . ' ' . $order->billing_data['last_name'],
                $order->billing_data['email'],
                $order->billing_data['phone']
            ));
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Import products from CSV
     */
    private function import_products() {
        if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] !== UPLOAD_ERR_OK) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Please select a valid CSV file.', 'smartshop') . '</p></div>';
            });
            return;
        }
        
        $file = $_FILES['import_file']['tmp_name'];
        $handle = fopen($file, 'r');
        
        if (!$handle) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Failed to read the CSV file.', 'smartshop') . '</p></div>';
            });
            return;
        }
        
        $imported = 0;
        $errors = 0;
        
        // Skip header row
        fgetcsv($handle);
        
        while (($data = fgetcsv($handle)) !== FALSE) {
            $product_data = array(
                'name' => $data[1] ?? '',
                'slug' => $data[2] ?? '',
                'description' => $data[3] ?? '',
                'price' => floatval($data[4] ?? 0),
                'sale_price' => !empty($data[5]) ? floatval($data[5]) : null,
                'sku' => $data[6] ?? '',
                'stock_quantity' => !empty($data[7]) ? intval($data[7]) : null,
                'stock_status' => $data[8] ?? 'instock',
                'image_url' => $data[10] ?? '',
                'status' => $data[11] ?? 'publish',
                'featured' => ($data[12] ?? '') === 'Yes' ? 1 : 0
            );
            
            if (SmartShop_Product::create_product($product_data)) {
                $imported++;
            } else {
                $errors++;
            }
        }
        
        fclose($handle);
        
        add_action('admin_notices', function() use ($imported, $errors) {
            echo '<div class="notice notice-success"><p>' . sprintf(__('Import completed. %d products imported, %d errors.', 'smartshop'), $imported, $errors) . '</p></div>';
        });
    }
    
    /**
     * Clear cache
     */
    private function clear_cache() {
        // Clear any plugin cache here
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success"><p>' . __('Cache cleared successfully.', 'smartshop') . '</p></div>';
        });
    }
    
    /**
     * Reset settings
     */
    private function reset_settings() {
        SmartShop_Settings::remove_all_options();
        SmartShop_Settings::set_default_options();
        
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success"><p>' . __('Settings reset to defaults.', 'smartshop') . '</p></div>';
        });
    }
    
    /**
     * Admin notices
     */
    public function admin_notices() {
        // Check for missing dependencies or configuration issues
        if (!SmartShop_Database::tables_exist()) {
            echo '<div class="notice notice-error"><p>' . __('SmartShop database tables are missing. Please deactivate and reactivate the plugin.', 'smartshop') . '</p></div>';
        }
    }
    
    /**
     * Activation notice
     */
    public function activation_notice() {
        echo '<div class="notice notice-success is-dismissible">';
        echo '<p>' . sprintf(
            __('SmartShop has been activated successfully! <a href="%s">Configure your shop settings</a> to get started.', 'smartshop'),
            admin_url('admin.php?page=smartshop-settings')
        ) . '</p>';
        echo '</div>';
    }
    
    /**
     * Plugin action links
     */
    public function plugin_action_links($links) {
        $action_links = array(
            'settings' => '<a href="' . admin_url('admin.php?page=smartshop-settings') . '">' . __('Settings', 'smartshop') . '</a>',
            'products' => '<a href="' . admin_url('admin.php?page=smartshop-products') . '">' . __('Products', 'smartshop') . '</a>',
        );
        
        return array_merge($action_links, $links);
    }
}
