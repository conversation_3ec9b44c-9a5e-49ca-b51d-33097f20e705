<?php
/**
 * Admin Add/Edit Product View
 * 
 * Form for adding or editing products
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$is_edit = isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['product_id']);
$product = null;

if ($is_edit) {
    $product_id = intval($_GET['product_id']);
    $product = SmartShop_Product::get_product($product_id);
    
    if (!$product) {
        wp_die(__('Product not found.', 'smartshop'));
    }
}

$page_title = $is_edit ? __('Edit Product', 'smartshop') : __('Add New Product', 'smartshop');
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php echo esc_html($page_title); ?></h1>
    
    <hr class="wp-header-end">
    
    <form method="post" action="" enctype="multipart/form-data">
        <?php wp_nonce_field('smartshop_admin_action'); ?>
        <input type="hidden" name="smartshop_action" value="<?php echo $is_edit ? 'edit_product' : 'add_product'; ?>">
        <?php if ($is_edit): ?>
            <input type="hidden" name="product_id" value="<?php echo esc_attr($product->id); ?>">
        <?php endif; ?>
        
        <div class="smartshop-form-container">
            
            <!-- Basic Information -->
            <div class="smartshop-card">
                <div class="smartshop-card-header">
                    <h2 class="smartshop-card-title"><?php _e('Basic Information', 'smartshop'); ?></h2>
                </div>
                
                <table class="smartshop-form-table">
                    <tr>
                        <th>
                            <label for="product_name"><?php _e('Product Name', 'smartshop'); ?> *</label>
                        </th>
                        <td>
                            <input type="text" id="product_name" name="product_name" 
                                   class="smartshop-form-input" 
                                   value="<?php echo $is_edit ? esc_attr($product->name) : ''; ?>" 
                                   required>
                        </td>
                    </tr>
                    
                    <tr>
                        <th>
                            <label for="product_description"><?php _e('Description', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <textarea id="product_description" name="product_description" 
                                      class="smartshop-form-textarea" rows="6">
                                <?php echo $is_edit ? esc_textarea($product->description) : ''; ?>
                            </textarea>
                        </td>
                    </tr>
                    
                    <tr>
                        <th>
                            <label for="product_short_description"><?php _e('Short Description', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <textarea id="product_short_description" name="product_short_description" 
                                      class="smartshop-form-textarea" rows="3">
                                <?php echo $is_edit ? esc_textarea($product->short_description) : ''; ?>
                            </textarea>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Pricing -->
            <div class="smartshop-card">
                <div class="smartshop-card-header">
                    <h2 class="smartshop-card-title"><?php _e('Pricing', 'smartshop'); ?></h2>
                </div>
                
                <table class="smartshop-form-table">
                    <tr>
                        <th>
                            <label for="product_price"><?php _e('Regular Price', 'smartshop'); ?> *</label>
                        </th>
                        <td>
                            <input type="number" id="product_price" name="product_price" 
                                   class="smartshop-form-input" step="0.01" min="0"
                                   value="<?php echo $is_edit ? esc_attr($product->price) : ''; ?>" 
                                   required>
                        </td>
                    </tr>
                    
                    <tr>
                        <th>
                            <label for="product_sale_price"><?php _e('Sale Price', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="product_sale_price" name="product_sale_price" 
                                   class="smartshop-form-input" step="0.01" min="0"
                                   value="<?php echo $is_edit && $product->sale_price ? esc_attr($product->sale_price) : ''; ?>">
                            <p class="smartshop-form-description">
                                <?php _e('Leave empty if not on sale.', 'smartshop'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Inventory -->
            <div class="smartshop-card">
                <div class="smartshop-card-header">
                    <h2 class="smartshop-card-title"><?php _e('Inventory', 'smartshop'); ?></h2>
                </div>
                
                <table class="smartshop-form-table">
                    <tr>
                        <th>
                            <label for="product_sku"><?php _e('SKU', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="product_sku" name="product_sku" 
                                   class="smartshop-form-input"
                                   value="<?php echo $is_edit ? esc_attr($product->sku) : ''; ?>">
                            <p class="smartshop-form-description">
                                <?php _e('Stock Keeping Unit - unique identifier for this product.', 'smartshop'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th>
                            <label for="manage_stock"><?php _e('Manage Stock', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <label>
                                <input type="checkbox" id="manage_stock" name="manage_stock" value="1"
                                       <?php echo ($is_edit && $product->manage_stock) ? 'checked' : ''; ?>>
                                <?php _e('Enable stock management', 'smartshop'); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr id="stock_quantity_row" style="<?php echo ($is_edit && !$product->manage_stock) ? 'display: none;' : ''; ?>">
                        <th>
                            <label for="stock_quantity"><?php _e('Stock Quantity', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="stock_quantity" name="stock_quantity" 
                                   class="smartshop-form-input" min="0"
                                   value="<?php echo $is_edit && $product->stock_quantity !== null ? esc_attr($product->stock_quantity) : ''; ?>">
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Category & Image -->
            <div class="smartshop-card">
                <div class="smartshop-card-header">
                    <h2 class="smartshop-card-title"><?php _e('Category & Image', 'smartshop'); ?></h2>
                </div>
                
                <table class="smartshop-form-table">
                    <tr>
                        <th>
                            <label for="product_category"><?php _e('Category', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <select id="product_category" name="product_category" class="smartshop-form-select">
                                <option value=""><?php _e('Select Category', 'smartshop'); ?></option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo esc_attr($category->id); ?>"
                                            <?php echo ($is_edit && $product->category_id == $category->id) ? 'selected' : ''; ?>>
                                        <?php echo esc_html($category->name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                    </tr>
                    
                    <tr>
                        <th>
                            <label for="product_image"><?php _e('Product Image', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <div class="smartshop-image-upload">
                                <div class="smartshop-image-preview">
                                    <?php if ($is_edit && $product->image_url): ?>
                                        <img src="<?php echo esc_url($product->image_url); ?>" alt="">
                                    <?php else: ?>
                                        <div class="smartshop-image-placeholder">
                                            <?php _e('Click to upload image', 'smartshop'); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="smartshop-image-actions">
                                    <button type="button" class="button smartshop-upload-btn">
                                        <?php _e('Upload Image', 'smartshop'); ?>
                                    </button>
                                    <button type="button" class="button smartshop-remove-image">
                                        <?php _e('Remove Image', 'smartshop'); ?>
                                    </button>
                                </div>
                                <input type="hidden" id="product_image" name="product_image" 
                                       value="<?php echo $is_edit ? esc_url($product->image_url) : ''; ?>">
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <th>
                            <label for="featured"><?php _e('Featured Product', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <label>
                                <input type="checkbox" id="featured" name="featured" value="1"
                                       <?php echo ($is_edit && $product->featured) ? 'checked' : ''; ?>>
                                <?php _e('Mark as featured product', 'smartshop'); ?>
                            </label>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Submit Button -->
            <div class="smartshop-form-actions">
                <button type="submit" class="button button-primary button-large">
                    <?php echo $is_edit ? __('Update Product', 'smartshop') : __('Add Product', 'smartshop'); ?>
                </button>
                
                <a href="<?php echo admin_url('admin.php?page=smartshop-products'); ?>" class="button button-secondary button-large">
                    <?php _e('Cancel', 'smartshop'); ?>
                </a>
            </div>
            
        </div>
    </form>
</div>

<script>
jQuery(document).ready(function($) {
    // Toggle stock quantity field
    $('#manage_stock').on('change', function() {
        if ($(this).is(':checked')) {
            $('#stock_quantity_row').show();
        } else {
            $('#stock_quantity_row').hide();
        }
    });
});
</script>

<style>
.smartshop-form-container {
    max-width: 800px;
}

.smartshop-form-actions {
    margin-top: 20px;
    padding: 20px;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
}

.smartshop-form-actions .button {
    margin-right: 10px;
}
</style>
