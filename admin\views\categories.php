<?php
/**
 * Admin Categories View
 * 
 * Displays the categories management page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1 class="wp-heading-inline">
        <?php _e('Product Categories', 'smartshop'); ?>
    </h1>
    
    <hr class="wp-header-end">
    
    <div class="smartshop-row">
        
        <!-- Add New Category Form -->
        <div class="smartshop-col smartshop-col-4">
            <div class="smartshop-card">
                <div class="smartshop-card-header">
                    <h2 class="smartshop-card-title"><?php _e('Add New Category', 'smartshop'); ?></h2>
                </div>
                
                <form method="post" action="">
                    <?php wp_nonce_field('smartshop_admin_action'); ?>
                    <input type="hidden" name="smartshop_action" value="add_category">
                    
                    <table class="smartshop-form-table">
                        <tr>
                            <th>
                                <label for="category_name"><?php _e('Name', 'smartshop'); ?> *</label>
                            </th>
                            <td>
                                <input type="text" id="category_name" name="category_name" 
                                       class="smartshop-form-input" required>
                            </td>
                        </tr>
                        
                        <tr>
                            <th>
                                <label for="category_slug"><?php _e('Slug', 'smartshop'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="category_slug" name="category_slug" 
                                       class="smartshop-form-input">
                                <p class="smartshop-form-description">
                                    <?php _e('Leave empty to auto-generate from name.', 'smartshop'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th>
                                <label for="parent_category"><?php _e('Parent Category', 'smartshop'); ?></label>
                            </th>
                            <td>
                                <select id="parent_category" name="parent_category" class="smartshop-form-select">
                                    <option value="0"><?php _e('None (Main Category)', 'smartshop'); ?></option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo esc_attr($category->id); ?>">
                                            <?php echo esc_html($category->name); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </td>
                        </tr>
                        
                        <tr>
                            <th>
                                <label for="category_description"><?php _e('Description', 'smartshop'); ?></label>
                            </th>
                            <td>
                                <textarea id="category_description" name="category_description" 
                                          class="smartshop-form-textarea" rows="4"></textarea>
                            </td>
                        </tr>
                        
                        <tr>
                            <th>
                                <label for="category_image"><?php _e('Category Image', 'smartshop'); ?></label>
                            </th>
                            <td>
                                <div class="smartshop-image-upload">
                                    <div class="smartshop-image-preview">
                                        <div class="smartshop-image-placeholder">
                                            <?php _e('Click to upload image', 'smartshop'); ?>
                                        </div>
                                    </div>
                                    <div class="smartshop-image-actions">
                                        <button type="button" class="button smartshop-upload-btn">
                                            <?php _e('Upload Image', 'smartshop'); ?>
                                        </button>
                                        <button type="button" class="button smartshop-remove-image">
                                            <?php _e('Remove Image', 'smartshop'); ?>
                                        </button>
                                    </div>
                                    <input type="hidden" id="category_image" name="category_image">
                                </div>
                            </td>
                        </tr>
                    </table>
                    
                    <div class="smartshop-form-actions">
                        <button type="submit" class="button button-primary">
                            <?php _e('Add Category', 'smartshop'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Categories List -->
        <div class="smartshop-col smartshop-col-8">
            <div class="smartshop-card">
                <div class="smartshop-card-header">
                    <h2 class="smartshop-card-title"><?php _e('Categories', 'smartshop'); ?></h2>
                </div>
                
                <?php if (!empty($categories)): ?>
                    <div class="smartshop-table-container">
                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th><?php _e('Image', 'smartshop'); ?></th>
                                    <th><?php _e('Name', 'smartshop'); ?></th>
                                    <th><?php _e('Slug', 'smartshop'); ?></th>
                                    <th><?php _e('Products', 'smartshop'); ?></th>
                                    <th><?php _e('Status', 'smartshop'); ?></th>
                                    <th><?php _e('Actions', 'smartshop'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($categories as $category): ?>
                                    <tr>
                                        <td>
                                            <?php if ($category->image_url): ?>
                                                <img src="<?php echo esc_url($category->image_url); ?>" 
                                                     alt="<?php echo esc_attr($category->name); ?>" 
                                                     style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                                            <?php else: ?>
                                                <div style="width: 40px; height: 40px; background: #f0f0f1; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 10px; color: #646970;">
                                                    <?php _e('No Image', 'smartshop'); ?>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo esc_html($category->name); ?></strong>
                                            <?php if ($category->description): ?>
                                                <br><small style="color: #646970;">
                                                    <?php echo esc_html(wp_trim_words($category->description, 10)); ?>
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <code><?php echo esc_html($category->slug); ?></code>
                                        </td>
                                        <td>
                                            <?php
                                            $product_count = SmartShop_Shortcodes::get_category_product_count($category->id);
                                            echo number_format($product_count);
                                            ?>
                                        </td>
                                        <td>
                                            <span class="smartshop-status smartshop-status-<?php echo esc_attr($category->status); ?>">
                                                <?php echo esc_html(ucfirst($category->status)); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="<?php echo admin_url('admin.php?page=smartshop-categories&action=edit&category_id=' . $category->id); ?>" 
                                               class="button button-small">
                                                <?php _e('Edit', 'smartshop'); ?>
                                            </a>
                                            
                                            <?php if ($product_count == 0): ?>
                                                <form method="post" style="display: inline-block; margin-left: 5px;">
                                                    <?php wp_nonce_field('smartshop_admin_action'); ?>
                                                    <input type="hidden" name="smartshop_action" value="delete_category">
                                                    <input type="hidden" name="category_id" value="<?php echo esc_attr($category->id); ?>">
                                                    <button type="submit" class="button button-small button-link-delete" 
                                                            onclick="return confirm('<?php _e('Are you sure you want to delete this category?', 'smartshop'); ?>')">
                                                        <?php _e('Delete', 'smartshop'); ?>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="notice notice-info" style="margin: 20px;">
                        <p><?php _e('No categories found. Add your first category using the form on the left.', 'smartshop'); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Auto-generate slug from name
    $('#category_name').on('input', function() {
        var name = $(this).val();
        var slug = name.toLowerCase()
                      .replace(/[^a-z0-9\s-]/g, '')
                      .replace(/\s+/g, '-')
                      .replace(/-+/g, '-')
                      .trim('-');
        $('#category_slug').val(slug);
    });
});
</script>
