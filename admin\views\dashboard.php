<?php
/**
 * Admin Dashboard View
 * 
 * Displays the main admin dashboard with statistics and quick actions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1 class="wp-heading-inline">
        <?php _e('SmartShop Dashboard', 'smartshop'); ?>
    </h1>
    
    <hr class="wp-header-end">
    
    <!-- Welcome Message -->
    <div class="smartshop-welcome-panel">
        <div class="welcome-panel-content">
            <h2><?php _e('Welcome to SmartShop!', 'smartshop'); ?></h2>
            <p class="about-description">
                <?php _e('Your modern eCommerce solution is ready to go. Here\'s a quick overview of your store.', 'smartshop'); ?>
            </p>
            
            <div class="welcome-panel-column-container">
                <div class="welcome-panel-column">
                    <h3><?php _e('Get Started', 'smartshop'); ?></h3>
                    <a class="button button-primary button-hero" href="<?php echo admin_url('admin.php?page=smartshop-add-product'); ?>">
                        <?php _e('Add Your First Product', 'smartshop'); ?>
                    </a>
                    <p><?php _e('Start building your catalog by adding products to your store.', 'smartshop'); ?></p>
                </div>
                
                <div class="welcome-panel-column">
                    <h3><?php _e('Customize', 'smartshop'); ?></h3>
                    <a class="button button-secondary" href="<?php echo admin_url('admin.php?page=smartshop-settings'); ?>">
                        <?php _e('Configure Settings', 'smartshop'); ?>
                    </a>
                    <p><?php _e('Set up your store colors, payment methods, and other preferences.', 'smartshop'); ?></p>
                </div>
                
                <div class="welcome-panel-column">
                    <h3><?php _e('View Store', 'smartshop'); ?></h3>
                    <a class="button button-secondary" href="<?php echo SmartShop_Public::get_shop_url(); ?>" target="_blank">
                        <?php _e('Visit Your Shop', 'smartshop'); ?>
                    </a>
                    <p><?php _e('See how your store looks to customers.', 'smartshop'); ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="smartshop-stats-grid">
        
        <!-- Total Products -->
        <div class="smartshop-stat-card">
            <div class="stat-icon products">
                <span class="dashicons dashicons-products"></span>
            </div>
            <div class="stat-content">
                <h3 class="stat-number"><?php echo number_format($stats['total_products']); ?></h3>
                <p class="stat-label"><?php _e('Total Products', 'smartshop'); ?></p>
                <a href="<?php echo admin_url('admin.php?page=smartshop-products'); ?>" class="stat-link">
                    <?php _e('Manage Products', 'smartshop'); ?>
                </a>
            </div>
        </div>
        
        <!-- Total Orders -->
        <div class="smartshop-stat-card">
            <div class="stat-icon orders">
                <span class="dashicons dashicons-cart"></span>
            </div>
            <div class="stat-content">
                <h3 class="stat-number"><?php echo number_format($stats['total_orders']); ?></h3>
                <p class="stat-label"><?php _e('Total Orders', 'smartshop'); ?></p>
                <a href="<?php echo admin_url('admin.php?page=smartshop-orders'); ?>" class="stat-link">
                    <?php _e('View Orders', 'smartshop'); ?>
                </a>
            </div>
        </div>
        
        <!-- Pending Orders -->
        <div class="smartshop-stat-card">
            <div class="stat-icon pending">
                <span class="dashicons dashicons-clock"></span>
            </div>
            <div class="stat-content">
                <h3 class="stat-number"><?php echo number_format($stats['pending_orders']); ?></h3>
                <p class="stat-label"><?php _e('Pending Orders', 'smartshop'); ?></p>
                <?php if ($stats['pending_orders'] > 0): ?>
                    <a href="<?php echo admin_url('admin.php?page=smartshop-orders&status=pending'); ?>" class="stat-link">
                        <?php _e('Process Orders', 'smartshop'); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Total Sales -->
        <div class="smartshop-stat-card">
            <div class="stat-icon sales">
                <span class="dashicons dashicons-money-alt"></span>
            </div>
            <div class="stat-content">
                <h3 class="stat-number"><?php echo SmartShop_Settings::format_currency($stats['total_sales']); ?></h3>
                <p class="stat-label"><?php _e('Total Sales', 'smartshop'); ?></p>
            </div>
        </div>
        
        <!-- This Month Sales -->
        <div class="smartshop-stat-card">
            <div class="stat-icon month-sales">
                <span class="dashicons dashicons-chart-line"></span>
            </div>
            <div class="stat-content">
                <h3 class="stat-number"><?php echo SmartShop_Settings::format_currency($stats['month_sales']); ?></h3>
                <p class="stat-label"><?php _e('This Month', 'smartshop'); ?></p>
            </div>
        </div>
        
        <!-- Categories -->
        <div class="smartshop-stat-card">
            <div class="stat-icon categories">
                <span class="dashicons dashicons-category"></span>
            </div>
            <div class="stat-content">
                <h3 class="stat-number"><?php echo number_format($stats['total_categories']); ?></h3>
                <p class="stat-label"><?php _e('Categories', 'smartshop'); ?></p>
                <a href="<?php echo admin_url('admin.php?page=smartshop-categories'); ?>" class="stat-link">
                    <?php _e('Manage Categories', 'smartshop'); ?>
                </a>
            </div>
        </div>
        
    </div>
    
    <!-- Alerts -->
    <?php if ($stats['low_stock_products'] > 0 || $stats['out_of_stock_products'] > 0): ?>
        <div class="smartshop-alerts">
            <h2><?php _e('Inventory Alerts', 'smartshop'); ?></h2>
            
            <?php if ($stats['out_of_stock_products'] > 0): ?>
                <div class="notice notice-error">
                    <p>
                        <strong><?php _e('Out of Stock:', 'smartshop'); ?></strong>
                        <?php printf(
                            _n('%d product is out of stock', '%d products are out of stock', $stats['out_of_stock_products'], 'smartshop'),
                            $stats['out_of_stock_products']
                        ); ?>
                        <a href="<?php echo admin_url('admin.php?page=smartshop-products&stock_status=outofstock'); ?>">
                            <?php _e('View products', 'smartshop'); ?>
                        </a>
                    </p>
                </div>
            <?php endif; ?>
            
            <?php if ($stats['low_stock_products'] > 0): ?>
                <div class="notice notice-warning">
                    <p>
                        <strong><?php _e('Low Stock:', 'smartshop'); ?></strong>
                        <?php printf(
                            _n('%d product has low stock', '%d products have low stock', $stats['low_stock_products'], 'smartshop'),
                            $stats['low_stock_products']
                        ); ?>
                        <a href="<?php echo admin_url('admin.php?page=smartshop-products&stock_status=low'); ?>">
                            <?php _e('View products', 'smartshop'); ?>
                        </a>
                    </p>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    
    <!-- Recent Orders -->
    <?php if (!empty($stats['recent_orders'])): ?>
        <div class="smartshop-recent-orders">
            <h2><?php _e('Recent Orders', 'smartshop'); ?></h2>
            
            <div class="smartshop-table-container">
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Order', 'smartshop'); ?></th>
                            <th><?php _e('Date', 'smartshop'); ?></th>
                            <th><?php _e('Status', 'smartshop'); ?></th>
                            <th><?php _e('Total', 'smartshop'); ?></th>
                            <th><?php _e('Customer', 'smartshop'); ?></th>
                            <th><?php _e('Actions', 'smartshop'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($stats['recent_orders'] as $order): ?>
                            <tr>
                                <td>
                                    <strong><?php echo esc_html($order->order_number); ?></strong>
                                </td>
                                <td>
                                    <?php echo date_i18n(get_option('date_format'), strtotime($order->created_at)); ?>
                                </td>
                                <td>
                                    <span class="order-status status-<?php echo esc_attr($order->status); ?>">
                                        <?php echo esc_html(ucfirst($order->status)); ?>
                                    </span>
                                </td>
                                <td>
                                    <strong><?php echo SmartShop_Settings::format_currency($order->total); ?></strong>
                                </td>
                                <td>
                                    <?php
                                    $billing_data = unserialize($order->billing_data);
                                    echo esc_html($billing_data['first_name'] . ' ' . $billing_data['last_name']);
                                    ?>
                                </td>
                                <td>
                                    <a href="<?php echo admin_url('admin.php?page=smartshop-orders&action=view&order_id=' . $order->id); ?>" 
                                       class="button button-small">
                                        <?php _e('View', 'smartshop'); ?>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <p class="view-all">
                <a href="<?php echo admin_url('admin.php?page=smartshop-orders'); ?>" class="button">
                    <?php _e('View All Orders', 'smartshop'); ?>
                </a>
            </p>
        </div>
    <?php endif; ?>
    
    <!-- Quick Actions -->
    <div class="smartshop-quick-actions">
        <h2><?php _e('Quick Actions', 'smartshop'); ?></h2>
        
        <div class="quick-actions-grid">
            <a href="<?php echo admin_url('admin.php?page=smartshop-add-product'); ?>" class="quick-action-card">
                <span class="dashicons dashicons-plus-alt"></span>
                <span><?php _e('Add Product', 'smartshop'); ?></span>
            </a>
            
            <a href="<?php echo admin_url('admin.php?page=smartshop-categories'); ?>" class="quick-action-card">
                <span class="dashicons dashicons-category"></span>
                <span><?php _e('Manage Categories', 'smartshop'); ?></span>
            </a>
            
            <a href="<?php echo admin_url('admin.php?page=smartshop-orders'); ?>" class="quick-action-card">
                <span class="dashicons dashicons-cart"></span>
                <span><?php _e('View Orders', 'smartshop'); ?></span>
            </a>
            
            <a href="<?php echo admin_url('admin.php?page=smartshop-settings'); ?>" class="quick-action-card">
                <span class="dashicons dashicons-admin-settings"></span>
                <span><?php _e('Settings', 'smartshop'); ?></span>
            </a>
            
            <a href="<?php echo admin_url('admin.php?page=smartshop-tools'); ?>" class="quick-action-card">
                <span class="dashicons dashicons-admin-tools"></span>
                <span><?php _e('Tools', 'smartshop'); ?></span>
            </a>
            
            <a href="<?php echo SmartShop_Public::get_shop_url(); ?>" target="_blank" class="quick-action-card">
                <span class="dashicons dashicons-external"></span>
                <span><?php _e('View Store', 'smartshop'); ?></span>
            </a>
        </div>
    </div>
    
</div>

<style>
/* Dashboard Styles */
.smartshop-welcome-panel {
    background: #fff;
    border: 1px solid #c3c4c7;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin: 20px 0;
    padding: 23px 10px 0;
    position: relative;
}

.welcome-panel-content {
    max-width: none;
}

.welcome-panel-column-container {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.welcome-panel-column {
    flex: 1;
    padding: 0 20px 20px;
}

.smartshop-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.smartshop-stat-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: box-shadow 0.2s ease;
}

.smartshop-stat-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #fff;
}

.stat-icon.products { background: #2271b1; }
.stat-icon.orders { background: #00a32a; }
.stat-icon.pending { background: #dba617; }
.stat-icon.sales { background: #8c8f94; }
.stat-icon.month-sales { background: #135e96; }
.stat-icon.categories { background: #d63638; }

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: #1d2327;
}

.stat-label {
    margin: 0 0 8px 0;
    color: #646970;
    font-size: 14px;
}

.stat-link {
    color: #2271b1;
    text-decoration: none;
    font-size: 13px;
}

.stat-link:hover {
    text-decoration: underline;
}

.smartshop-alerts {
    margin: 20px 0;
}

.smartshop-recent-orders {
    background: #fff;
    border: 1px solid #c3c4c7;
    margin: 20px 0;
    padding: 20px;
}

.smartshop-table-container {
    overflow-x: auto;
}

.order-status {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-processing { background: #d1ecf1; color: #0c5460; }
.status-completed { background: #d4edda; color: #155724; }
.status-cancelled { background: #f8d7da; color: #721c24; }

.smartshop-quick-actions {
    margin: 20px 0;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.quick-action-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    text-decoration: none;
    color: #1d2327;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.quick-action-card:hover {
    background: #f6f7f7;
    color: #2271b1;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.quick-action-card .dashicons {
    font-size: 24px;
}

@media (max-width: 768px) {
    .welcome-panel-column-container {
        flex-direction: column;
        gap: 10px;
    }
    
    .smartshop-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
