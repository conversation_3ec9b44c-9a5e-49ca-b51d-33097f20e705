<?php
/**
 * Admin Orders View
 * 
 * Displays the orders management page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$order_statuses = SmartShop_Admin_Orders::get_order_statuses();
$payment_methods = SmartShop_Admin_Orders::get_payment_methods();
?>

<div class="wrap">
    <h1 class="wp-heading-inline">
        <?php _e('Orders', 'smartshop'); ?>
    </h1>
    
    <hr class="wp-header-end">
    
    <!-- Status Filter -->
    <div class="tablenav top">
        <div class="alignleft actions">
            <select name="status_filter" id="status_filter">
                <option value=""><?php _e('All Statuses', 'smartshop'); ?></option>
                <?php foreach ($order_statuses as $status_key => $status_label): ?>
                    <option value="<?php echo esc_attr($status_key); ?>" 
                            <?php selected($status_filter, $status_key); ?>>
                        <?php echo esc_html($status_label); ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <button type="button" class="button" onclick="filterOrders()">
                <?php _e('Filter', 'smartshop'); ?>
            </button>
        </div>
    </div>
    
    <?php if (!empty($orders)): ?>
        <div class="smartshop-table-container">
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Order', 'smartshop'); ?></th>
                        <th><?php _e('Date', 'smartshop'); ?></th>
                        <th><?php _e('Customer', 'smartshop'); ?></th>
                        <th><?php _e('Status', 'smartshop'); ?></th>
                        <th><?php _e('Payment', 'smartshop'); ?></th>
                        <th><?php _e('Total', 'smartshop'); ?></th>
                        <th><?php _e('Actions', 'smartshop'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($orders as $order): ?>
                        <tr>
                            <td>
                                <strong><?php echo esc_html($order->order_number); ?></strong>
                                <br>
                                <small style="color: #646970;">
                                    ID: <?php echo esc_html($order->id); ?>
                                </small>
                            </td>
                            <td>
                                <?php echo date_i18n(get_option('date_format'), strtotime($order->created_at)); ?>
                                <br>
                                <small style="color: #646970;">
                                    <?php echo date_i18n(get_option('time_format'), strtotime($order->created_at)); ?>
                                </small>
                            </td>
                            <td>
                                <?php
                                $billing_data = unserialize($order->billing_data);
                                echo esc_html($billing_data['first_name'] . ' ' . $billing_data['last_name']);
                                ?>
                                <br>
                                <small style="color: #646970;">
                                    <a href="mailto:<?php echo esc_attr($billing_data['email']); ?>">
                                        <?php echo esc_html($billing_data['email']); ?>
                                    </a>
                                </small>
                            </td>
                            <td>
                                <form method="post" style="display: inline-block;">
                                    <?php wp_nonce_field('smartshop_admin_action'); ?>
                                    <input type="hidden" name="smartshop_action" value="update_order_status">
                                    <input type="hidden" name="order_id" value="<?php echo esc_attr($order->id); ?>">
                                    
                                    <select name="order_status" onchange="this.form.submit()" 
                                            class="smartshop-status-select">
                                        <?php foreach ($order_statuses as $status_key => $status_label): ?>
                                            <option value="<?php echo esc_attr($status_key); ?>" 
                                                    <?php selected($order->status, $status_key); ?>>
                                                <?php echo esc_html($status_label); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </form>
                            </td>
                            <td>
                                <span class="smartshop-status smartshop-status-<?php echo esc_attr($order->payment_status); ?>">
                                    <?php echo esc_html(ucfirst($order->payment_status)); ?>
                                </span>
                                <br>
                                <small style="color: #646970;">
                                    <?php 
                                    $payment_method = isset($payment_methods[$order->payment_method]) 
                                        ? $payment_methods[$order->payment_method] 
                                        : ucfirst(str_replace('_', ' ', $order->payment_method));
                                    echo esc_html($payment_method);
                                    ?>
                                </small>
                            </td>
                            <td>
                                <strong><?php echo SmartShop_Settings::format_currency($order->total); ?></strong>
                                <br>
                                <small style="color: #646970;">
                                    <?php printf(__('%d items', 'smartshop'), count(unserialize($order->order_items ?? '[]'))); ?>
                                </small>
                            </td>
                            <td>
                                <a href="<?php echo admin_url('admin.php?page=smartshop-orders&action=view&order_id=' . $order->id); ?>" 
                                   class="button button-small">
                                    <?php _e('View', 'smartshop'); ?>
                                </a>
                                
                                <form method="post" style="display: inline-block; margin-left: 5px;">
                                    <?php wp_nonce_field('smartshop_admin_action'); ?>
                                    <input type="hidden" name="smartshop_action" value="delete_order">
                                    <input type="hidden" name="order_id" value="<?php echo esc_attr($order->id); ?>">
                                    <button type="submit" class="button button-small button-link-delete" 
                                            onclick="return confirm('<?php _e('Are you sure you want to delete this order?', 'smartshop'); ?>')">
                                        <?php _e('Delete', 'smartshop'); ?>
                                    </button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="tablenav bottom">
                <div class="tablenav-pages">
                    <?php
                    $page_links = paginate_links(array(
                        'base' => add_query_arg('paged', '%#%'),
                        'format' => '',
                        'prev_text' => __('&laquo;'),
                        'next_text' => __('&raquo;'),
                        'total' => $total_pages,
                        'current' => $current_page
                    ));
                    
                    if ($page_links) {
                        echo '<span class="displaying-num">' . 
                             sprintf(_n('%s item', '%s items', $total_orders, 'smartshop'), number_format_i18n($total_orders)) . 
                             '</span>';
                        echo $page_links;
                    }
                    ?>
                </div>
            </div>
        <?php endif; ?>
        
    <?php else: ?>
        <div class="notice notice-info">
            <p>
                <?php if ($status_filter): ?>
                    <?php printf(__('No orders found with status "%s".', 'smartshop'), $order_statuses[$status_filter]); ?>
                    <a href="<?php echo admin_url('admin.php?page=smartshop-orders'); ?>">
                        <?php _e('View all orders', 'smartshop'); ?>
                    </a>
                <?php else: ?>
                    <?php _e('No orders found. Orders will appear here when customers place them.', 'smartshop'); ?>
                <?php endif; ?>
            </p>
        </div>
    <?php endif; ?>
</div>

<script>
function filterOrders() {
    var status = document.getElementById('status_filter').value;
    var url = new URL(window.location.href);
    
    if (status) {
        url.searchParams.set('status', status);
    } else {
        url.searchParams.delete('status');
    }
    
    url.searchParams.delete('paged'); // Reset pagination
    window.location.href = url.toString();
}

// Auto-submit status changes
jQuery(document).ready(function($) {
    $('.smartshop-status-select').on('change', function() {
        var $form = $(this).closest('form');
        var $select = $(this);
        
        // Add loading state
        $select.prop('disabled', true);
        
        // Submit form
        $form.submit();
    });
});
</script>

<style>
.smartshop-status-select {
    font-size: 12px;
    padding: 2px 4px;
    border-radius: 3px;
    border: 1px solid #ddd;
}

.tablenav {
    margin: 10px 0;
}

.tablenav .actions {
    padding: 2px 8px 0 0;
}

.tablenav .actions select {
    margin-right: 5px;
}
</style>
