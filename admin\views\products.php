<?php
/**
 * Admin Products View
 * 
 * Displays the products management page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1 class="wp-heading-inline">
        <?php _e('Products', 'smartshop'); ?>
    </h1>
    
    <a href="<?php echo admin_url('admin.php?page=smartshop-add-product'); ?>" class="page-title-action">
        <?php _e('Add New Product', 'smartshop'); ?>
    </a>
    
    <hr class="wp-header-end">
    
    <?php if (!empty($products)): ?>
        <div class="smartshop-table-container">
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Image', 'smartshop'); ?></th>
                        <th><?php _e('Name', 'smartshop'); ?></th>
                        <th><?php _e('SKU', 'smartshop'); ?></th>
                        <th><?php _e('Price', 'smartshop'); ?></th>
                        <th><?php _e('Stock', 'smartshop'); ?></th>
                        <th><?php _e('Status', 'smartshop'); ?></th>
                        <th><?php _e('Actions', 'smartshop'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($products as $product): ?>
                        <tr>
                            <td>
                                <?php if ($product->image_url): ?>
                                    <img src="<?php echo esc_url($product->image_url); ?>" 
                                         alt="<?php echo esc_attr($product->name); ?>" 
                                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px;">
                                <?php else: ?>
                                    <div style="width: 50px; height: 50px; background: #f0f0f1; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #646970;">
                                        <?php _e('No Image', 'smartshop'); ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong><?php echo esc_html($product->name); ?></strong>
                                <?php if ($product->featured): ?>
                                    <span class="smartshop-status smartshop-status-featured" style="background: #f59e0b; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin-left: 8px;">
                                        <?php _e('Featured', 'smartshop'); ?>
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo esc_html($product->sku ?: '-'); ?></td>
                            <td>
                                <?php if ($product->sale_price !== null && $product->sale_price < $product->price): ?>
                                    <span style="text-decoration: line-through; color: #646970;">
                                        <?php echo SmartShop_Settings::format_currency($product->price); ?>
                                    </span><br>
                                    <strong style="color: #d63638;">
                                        <?php echo SmartShop_Settings::format_currency($product->sale_price); ?>
                                    </strong>
                                <?php else: ?>
                                    <strong><?php echo SmartShop_Settings::format_currency($product->price); ?></strong>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($product->manage_stock): ?>
                                    <?php if ($product->stock_quantity <= 0): ?>
                                        <span class="smartshop-status smartshop-status-outofstock">
                                            <?php _e('Out of Stock', 'smartshop'); ?>
                                        </span>
                                    <?php elseif ($product->stock_quantity <= SmartShop_Settings::get_option('low_stock_threshold', 5)): ?>
                                        <span class="smartshop-status smartshop-status-pending">
                                            <?php printf(__('%d (Low)', 'smartshop'), $product->stock_quantity); ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="smartshop-status smartshop-status-instock">
                                            <?php echo $product->stock_quantity; ?>
                                        </span>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="smartshop-status smartshop-status-instock">
                                        <?php _e('In Stock', 'smartshop'); ?>
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="smartshop-status smartshop-status-<?php echo esc_attr($product->status); ?>">
                                    <?php echo esc_html(ucfirst($product->status)); ?>
                                </span>
                            </td>
                            <td>
                                <a href="<?php echo admin_url('admin.php?page=smartshop-add-product&action=edit&product_id=' . $product->id); ?>" 
                                   class="button button-small">
                                    <?php _e('Edit', 'smartshop'); ?>
                                </a>
                                
                                <form method="post" style="display: inline-block; margin-left: 5px;">
                                    <?php wp_nonce_field('smartshop_admin_action'); ?>
                                    <input type="hidden" name="smartshop_action" value="delete_product">
                                    <input type="hidden" name="product_id" value="<?php echo esc_attr($product->id); ?>">
                                    <button type="submit" class="button button-small button-link-delete" 
                                            onclick="return confirm('<?php _e('Are you sure you want to delete this product?', 'smartshop'); ?>')">
                                        <?php _e('Delete', 'smartshop'); ?>
                                    </button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="notice notice-info">
            <p>
                <?php _e('No products found.', 'smartshop'); ?>
                <a href="<?php echo admin_url('admin.php?page=smartshop-add-product'); ?>">
                    <?php _e('Add your first product', 'smartshop'); ?>
                </a>
            </p>
        </div>
    <?php endif; ?>
</div>

<style>
.smartshop-status-featured {
    background: #f59e0b !important;
    color: white !important;
}
</style>
