<?php
/**
 * Admin Settings View
 * 
 * Displays the plugin settings page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$tabs = SmartShop_Admin_Settings::get_setting_tabs();
$currencies = SmartShop_Admin_Settings::get_currencies();
?>

<div class="wrap">
    <h1 class="wp-heading-inline">
        <?php _e('SmartShop Settings', 'smartshop'); ?>
    </h1>
    
    <hr class="wp-header-end">
    
    <!-- Settings Tabs -->
    <div class="smartshop-tabs">
        <ul class="smartshop-tabs-nav">
            <?php foreach ($tabs as $tab_key => $tab_label): ?>
                <li>
                    <a href="#<?php echo esc_attr($tab_key); ?>" 
                       class="<?php echo $active_tab === $tab_key ? 'active' : ''; ?>">
                        <?php echo esc_html($tab_label); ?>
                    </a>
                </li>
            <?php endforeach; ?>
        </ul>
    </div>
    
    <!-- General Settings -->
    <div id="general" class="smartshop-tab-content <?php echo $active_tab === 'general' ? 'active' : ''; ?>">
        <form method="post" action="">
            <?php wp_nonce_field('smartshop_settings_action'); ?>
            <input type="hidden" name="smartshop_action" value="save_settings">
            <input type="hidden" name="tab" value="general">
            
            <div class="smartshop-card">
                <div class="smartshop-card-header">
                    <h2 class="smartshop-card-title"><?php _e('General Settings', 'smartshop'); ?></h2>
                </div>
                
                <table class="smartshop-form-table">
                    <tr>
                        <th>
                            <label for="shop_name"><?php _e('Shop Name', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="shop_name" name="shop_name" 
                                   class="smartshop-form-input"
                                   value="<?php echo esc_attr(SmartShop_Settings::get_option('shop_name', get_bloginfo('name'))); ?>">
                        </td>
                    </tr>
                    
                    <tr>
                        <th>
                            <label for="currency"><?php _e('Currency', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <select id="currency" name="currency" class="smartshop-form-select">
                                <?php foreach ($currencies as $code => $currency): ?>
                                    <option value="<?php echo esc_attr($code); ?>"
                                            <?php selected(SmartShop_Settings::get_option('currency', 'USD'), $code); ?>>
                                        <?php echo esc_html($currency['name'] . ' (' . $currency['symbol'] . ')'); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                    </tr>
                    
                    <tr>
                        <th>
                            <label for="currency_position"><?php _e('Currency Position', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <select id="currency_position" name="currency_position" class="smartshop-form-select">
                                <option value="left" <?php selected(SmartShop_Settings::get_option('currency_position', 'left'), 'left'); ?>>
                                    <?php _e('Left ($99)', 'smartshop'); ?>
                                </option>
                                <option value="right" <?php selected(SmartShop_Settings::get_option('currency_position', 'left'), 'right'); ?>>
                                    <?php _e('Right (99$)', 'smartshop'); ?>
                                </option>
                            </select>
                        </td>
                    </tr>
                    
                    <tr>
                        <th>
                            <label for="products_per_page"><?php _e('Products Per Page', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="products_per_page" name="products_per_page" 
                                   class="smartshop-form-input" min="1" max="100"
                                   value="<?php echo esc_attr(SmartShop_Settings::get_option('products_per_page', 12)); ?>">
                        </td>
                    </tr>
                    
                    <tr>
                        <th><?php _e('Features', 'smartshop'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_search" value="1"
                                       <?php checked(SmartShop_Settings::get_option('enable_search', 1)); ?>>
                                <?php _e('Enable product search', 'smartshop'); ?>
                            </label>
                            <br><br>
                            
                            <label>
                                <input type="checkbox" name="enable_filters" value="1"
                                       <?php checked(SmartShop_Settings::get_option('enable_filters', 1)); ?>>
                                <?php _e('Enable product filters', 'smartshop'); ?>
                            </label>
                            <br><br>
                            
                            <label>
                                <input type="checkbox" name="enable_user_registration" value="1"
                                       <?php checked(SmartShop_Settings::get_option('enable_user_registration', 1)); ?>>
                                <?php _e('Enable user registration', 'smartshop'); ?>
                            </label>
                            <br><br>
                            
                            <label>
                                <input type="checkbox" name="enable_guest_checkout" value="1"
                                       <?php checked(SmartShop_Settings::get_option('enable_guest_checkout', 1)); ?>>
                                <?php _e('Enable guest checkout', 'smartshop'); ?>
                            </label>
                        </td>
                    </tr>
                </table>
                
                <div class="smartshop-form-actions">
                    <button type="submit" class="button button-primary">
                        <?php _e('Save Settings', 'smartshop'); ?>
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Payment Settings -->
    <div id="payment" class="smartshop-tab-content <?php echo $active_tab === 'payment' ? 'active' : ''; ?>">
        <form method="post" action="">
            <?php wp_nonce_field('smartshop_settings_action'); ?>
            <input type="hidden" name="smartshop_action" value="save_settings">
            <input type="hidden" name="tab" value="payment">
            
            <div class="smartshop-card">
                <div class="smartshop-card-header">
                    <h2 class="smartshop-card-title"><?php _e('Payment Methods', 'smartshop'); ?></h2>
                </div>
                
                <table class="smartshop-form-table">
                    <tr>
                        <th><?php _e('Cash on Delivery', 'smartshop'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_cod" value="1"
                                       <?php checked(SmartShop_Settings::get_option('enable_cod', 1)); ?>>
                                <?php _e('Enable Cash on Delivery', 'smartshop'); ?>
                            </label>
                            <br><br>
                            
                            <label for="cod_instructions"><?php _e('COD Instructions:', 'smartshop'); ?></label>
                            <textarea id="cod_instructions" name="cod_instructions" 
                                      class="smartshop-form-textarea" rows="3">
                                <?php echo esc_textarea(SmartShop_Settings::get_option('cod_instructions', __('Pay with cash upon delivery.', 'smartshop'))); ?>
                            </textarea>
                        </td>
                    </tr>
                    
                    <tr>
                        <th><?php _e('Manual Payment', 'smartshop'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_manual_payment" value="1"
                                       <?php checked(SmartShop_Settings::get_option('enable_manual_payment', 0)); ?>>
                                <?php _e('Enable Manual Payment', 'smartshop'); ?>
                            </label>
                            <br><br>
                            
                            <label for="manual_payment_instructions"><?php _e('Payment Instructions:', 'smartshop'); ?></label>
                            <textarea id="manual_payment_instructions" name="manual_payment_instructions" 
                                      class="smartshop-form-textarea" rows="5">
                                <?php echo esc_textarea(SmartShop_Settings::get_option('manual_payment_instructions', __('Please transfer the amount to our bank account and send us the receipt.', 'smartshop'))); ?>
                            </textarea>
                        </td>
                    </tr>
                </table>
            </div>
            
            <div class="smartshop-card">
                <div class="smartshop-card-header">
                    <h2 class="smartshop-card-title"><?php _e('Shipping & Tax', 'smartshop'); ?></h2>
                </div>
                
                <table class="smartshop-form-table">
                    <tr>
                        <th><?php _e('Shipping', 'smartshop'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_shipping" value="1"
                                       <?php checked(SmartShop_Settings::get_option('enable_shipping', 0)); ?>>
                                <?php _e('Enable shipping cost', 'smartshop'); ?>
                            </label>
                            <br><br>
                            
                            <label for="shipping_cost"><?php _e('Shipping Cost:', 'smartshop'); ?></label>
                            <input type="number" id="shipping_cost" name="shipping_cost" 
                                   class="smartshop-form-input" step="0.01" min="0"
                                   value="<?php echo esc_attr(SmartShop_Settings::get_option('shipping_cost', 0)); ?>">
                            <br><br>
                            
                            <label for="free_shipping_threshold"><?php _e('Free Shipping Threshold:', 'smartshop'); ?></label>
                            <input type="number" id="free_shipping_threshold" name="free_shipping_threshold" 
                                   class="smartshop-form-input" step="0.01" min="0"
                                   value="<?php echo esc_attr(SmartShop_Settings::get_option('free_shipping_threshold', 0)); ?>">
                            <p class="smartshop-form-description">
                                <?php _e('Set to 0 to disable free shipping.', 'smartshop'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th><?php _e('Tax', 'smartshop'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_tax" value="1"
                                       <?php checked(SmartShop_Settings::get_option('enable_tax', 0)); ?>>
                                <?php _e('Enable tax calculation', 'smartshop'); ?>
                            </label>
                            <br><br>
                            
                            <label for="tax_rate"><?php _e('Tax Rate (%):', 'smartshop'); ?></label>
                            <input type="number" id="tax_rate" name="tax_rate" 
                                   class="smartshop-form-input" step="0.01" min="0" max="100"
                                   value="<?php echo esc_attr(SmartShop_Settings::get_option('tax_rate', 0)); ?>">
                        </td>
                    </tr>
                </table>
                
                <div class="smartshop-form-actions">
                    <button type="submit" class="button button-primary">
                        <?php _e('Save Settings', 'smartshop'); ?>
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Email Settings -->
    <div id="email" class="smartshop-tab-content <?php echo $active_tab === 'email' ? 'active' : ''; ?>">
        <form method="post" action="">
            <?php wp_nonce_field('smartshop_settings_action'); ?>
            <input type="hidden" name="smartshop_action" value="save_settings">
            <input type="hidden" name="tab" value="email">
            
            <div class="smartshop-card">
                <div class="smartshop-card-header">
                    <h2 class="smartshop-card-title"><?php _e('Email Settings', 'smartshop'); ?></h2>
                </div>
                
                <table class="smartshop-form-table">
                    <tr>
                        <th>
                            <label for="admin_email"><?php _e('Admin Email', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <input type="email" id="admin_email" name="admin_email" 
                                   class="smartshop-form-input"
                                   value="<?php echo esc_attr(SmartShop_Settings::get_option('admin_email', get_option('admin_email'))); ?>">
                            <p class="smartshop-form-description">
                                <?php _e('Email address to receive order notifications.', 'smartshop'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th>
                            <label for="from_email"><?php _e('From Email', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <input type="email" id="from_email" name="from_email" 
                                   class="smartshop-form-input"
                                   value="<?php echo esc_attr(SmartShop_Settings::get_option('from_email', get_option('admin_email'))); ?>">
                        </td>
                    </tr>
                    
                    <tr>
                        <th>
                            <label for="from_name"><?php _e('From Name', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="from_name" name="from_name" 
                                   class="smartshop-form-input"
                                   value="<?php echo esc_attr(SmartShop_Settings::get_option('from_name', get_bloginfo('name'))); ?>">
                        </td>
                    </tr>
                    
                    <tr>
                        <th><?php _e('Email Notifications', 'smartshop'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_order_emails" value="1"
                                       <?php checked(SmartShop_Settings::get_option('enable_order_emails', 1)); ?>>
                                <?php _e('Send order confirmation emails', 'smartshop'); ?>
                            </label>
                        </td>
                    </tr>
                </table>
                
                <div class="smartshop-form-actions">
                    <button type="submit" class="button button-primary">
                        <?php _e('Save Settings', 'smartshop'); ?>
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Appearance Settings -->
    <div id="appearance" class="smartshop-tab-content <?php echo $active_tab === 'appearance' ? 'active' : ''; ?>">
        <form method="post" action="">
            <?php wp_nonce_field('smartshop_settings_action'); ?>
            <input type="hidden" name="smartshop_action" value="save_settings">
            <input type="hidden" name="tab" value="appearance">
            
            <div class="smartshop-card">
                <div class="smartshop-card-header">
                    <h2 class="smartshop-card-title"><?php _e('Colors & Branding', 'smartshop'); ?></h2>
                </div>
                
                <table class="smartshop-form-table">
                    <tr>
                        <th>
                            <label for="primary_color"><?php _e('Primary Color', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="primary_color" name="primary_color" 
                                   class="smartshop-color-picker"
                                   value="<?php echo esc_attr(SmartShop_Settings::get_option('primary_color', '#3b82f6')); ?>">
                        </td>
                    </tr>
                    
                    <tr>
                        <th>
                            <label for="secondary_color"><?php _e('Secondary Color', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="secondary_color" name="secondary_color" 
                                   class="smartshop-color-picker"
                                   value="<?php echo esc_attr(SmartShop_Settings::get_option('secondary_color', '#1e40af')); ?>">
                        </td>
                    </tr>
                    
                    <tr>
                        <th>
                            <label for="accent_color"><?php _e('Accent Color', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="accent_color" name="accent_color" 
                                   class="smartshop-color-picker"
                                   value="<?php echo esc_attr(SmartShop_Settings::get_option('accent_color', '#f59e0b')); ?>">
                        </td>
                    </tr>
                </table>
            </div>
            
            <div class="smartshop-card">
                <div class="smartshop-card-header">
                    <h2 class="smartshop-card-title"><?php _e('Layout Settings', 'smartshop'); ?></h2>
                </div>
                
                <table class="smartshop-form-table">
                    <tr>
                        <th>
                            <label for="grid_columns_desktop"><?php _e('Desktop Columns', 'smartshop'); ?></label>
                        </th>
                        <td>
                            <select id="grid_columns_desktop" name="grid_columns_desktop" class="smartshop-form-select">
                                <?php for ($i = 2; $i <= 6; $i++): ?>
                                    <option value="<?php echo $i; ?>" 
                                            <?php selected(SmartShop_Settings::get_option('grid_columns_desktop', 4), $i); ?>>
                                        <?php echo $i; ?> <?php _e('Columns', 'smartshop'); ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </td>
                    </tr>
                    
                    <tr>
                        <th><?php _e('Display Options', 'smartshop'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="show_breadcrumbs" value="1"
                                       <?php checked(SmartShop_Settings::get_option('show_breadcrumbs', 1)); ?>>
                                <?php _e('Show breadcrumbs', 'smartshop'); ?>
                            </label>
                            <br><br>
                            
                            <label>
                                <input type="checkbox" name="show_product_count" value="1"
                                       <?php checked(SmartShop_Settings::get_option('show_product_count', 1)); ?>>
                                <?php _e('Show product count', 'smartshop'); ?>
                            </label>
                            <br><br>
                            
                            <label>
                                <input type="checkbox" name="show_sorting" value="1"
                                       <?php checked(SmartShop_Settings::get_option('show_sorting', 1)); ?>>
                                <?php _e('Show sorting options', 'smartshop'); ?>
                            </label>
                        </td>
                    </tr>
                </table>
                
                <div class="smartshop-form-actions">
                    <button type="submit" class="button button-primary">
                        <?php _e('Save Settings', 'smartshop'); ?>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Initialize color pickers
    if ($.fn.wpColorPicker) {
        $('.smartshop-color-picker').wpColorPicker();
    }
    
    // Auto-update currency symbol when currency changes
    $('#currency').on('change', function() {
        var currency = $(this).val();
        var currencies = <?php echo json_encode($currencies); ?>;
        
        if (currencies[currency]) {
            $('#currency_symbol').val(currencies[currency].symbol);
        }
    });
});
</script>
