<?php
/**
 * Admin Tools View
 * 
 * Provides import/export and maintenance tools
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1 class="wp-heading-inline">
        <?php _e('SmartShop Tools', 'smartshop'); ?>
    </h1>
    
    <hr class="wp-header-end">
    
    <div class="smartshop-tools-container">
        
        <!-- Export Tools -->
        <div class="smartshop-card">
            <div class="smartshop-card-header">
                <h2 class="smartshop-card-title"><?php _e('Export Data', 'smartshop'); ?></h2>
                <p class="smartshop-card-description">
                    <?php _e('Export your store data for backup or migration purposes.', 'smartshop'); ?>
                </p>
            </div>
            
            <div class="export-tools">
                <form method="post" action="">
                    <?php wp_nonce_field('smartshop_tools_action'); ?>
                    
                    <div class="tool-section">
                        <h3><?php _e('Export Products', 'smartshop'); ?></h3>
                        <p><?php _e('Download all products as a CSV file.', 'smartshop'); ?></p>
                        <button type="submit" name="smartshop_action" value="export_products" class="button button-secondary">
                            <span class="dashicons dashicons-download"></span>
                            <?php _e('Export Products', 'smartshop'); ?>
                        </button>
                    </div>
                    
                    <div class="tool-section">
                        <h3><?php _e('Export Orders', 'smartshop'); ?></h3>
                        <p><?php _e('Download all orders as a CSV file.', 'smartshop'); ?></p>
                        <button type="submit" name="smartshop_action" value="export_orders" class="button button-secondary">
                            <span class="dashicons dashicons-download"></span>
                            <?php _e('Export Orders', 'smartshop'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Import Tools -->
        <div class="smartshop-card">
            <div class="smartshop-card-header">
                <h2 class="smartshop-card-title"><?php _e('Import Data', 'smartshop'); ?></h2>
                <p class="smartshop-card-description">
                    <?php _e('Import products from CSV files. Make sure your CSV follows the correct format.', 'smartshop'); ?>
                </p>
            </div>
            
            <div class="import-tools">
                <form method="post" action="" enctype="multipart/form-data">
                    <?php wp_nonce_field('smartshop_tools_action'); ?>
                    
                    <div class="tool-section">
                        <h3><?php _e('Import Products', 'smartshop'); ?></h3>
                        <p><?php _e('Upload a CSV file with product data. Required columns: Name, Price, Description.', 'smartshop'); ?></p>
                        
                        <div class="file-upload-section">
                            <input type="file" name="import_file" accept=".csv" required>
                            <button type="submit" name="smartshop_action" value="import_products" class="button button-primary">
                                <span class="dashicons dashicons-upload"></span>
                                <?php _e('Import Products', 'smartshop'); ?>
                            </button>
                        </div>
                        
                        <div class="import-help">
                            <h4><?php _e('CSV Format Example:', 'smartshop'); ?></h4>
                            <code>
                                ID,Name,Slug,Description,Price,Sale Price,SKU,Stock Quantity,Stock Status,Category,Image URL,Status,Featured<br>
                                ,Product Name,,Product description,29.99,24.99,SKU123,100,instock,Electronics,https://example.com/image.jpg,publish,No
                            </code>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Maintenance Tools -->
        <div class="smartshop-card">
            <div class="smartshop-card-header">
                <h2 class="smartshop-card-title"><?php _e('Maintenance', 'smartshop'); ?></h2>
                <p class="smartshop-card-description">
                    <?php _e('Tools for maintaining and optimizing your store.', 'smartshop'); ?>
                </p>
            </div>
            
            <div class="maintenance-tools">
                <form method="post" action="">
                    <?php wp_nonce_field('smartshop_tools_action'); ?>
                    
                    <div class="tool-section">
                        <h3><?php _e('Clear Cache', 'smartshop'); ?></h3>
                        <p><?php _e('Clear all plugin cache and temporary data.', 'smartshop'); ?></p>
                        <button type="submit" name="smartshop_action" value="clear_cache" class="button button-secondary">
                            <span class="dashicons dashicons-update"></span>
                            <?php _e('Clear Cache', 'smartshop'); ?>
                        </button>
                    </div>
                    
                    <div class="tool-section">
                        <h3><?php _e('Reset Settings', 'smartshop'); ?></h3>
                        <p class="warning-text"><?php _e('⚠️ This will reset all plugin settings to defaults. This action cannot be undone.', 'smartshop'); ?></p>
                        <button type="submit" name="smartshop_action" value="reset_settings" 
                                class="button button-secondary smartshop-confirm" 
                                data-confirm="<?php _e('Are you sure you want to reset all settings? This cannot be undone.', 'smartshop'); ?>">
                            <span class="dashicons dashicons-admin-generic"></span>
                            <?php _e('Reset Settings', 'smartshop'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- System Information -->
        <div class="smartshop-card">
            <div class="smartshop-card-header">
                <h2 class="smartshop-card-title"><?php _e('System Information', 'smartshop'); ?></h2>
                <p class="smartshop-card-description">
                    <?php _e('System information for troubleshooting and support.', 'smartshop'); ?>
                </p>
            </div>
            
            <div class="system-info">
                <table class="smartshop-table">
                    <tbody>
                        <tr>
                            <th><?php _e('Plugin Version', 'smartshop'); ?></th>
                            <td><?php echo SMARTSHOP_VERSION; ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('WordPress Version', 'smartshop'); ?></th>
                            <td><?php echo get_bloginfo('version'); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('PHP Version', 'smartshop'); ?></th>
                            <td><?php echo PHP_VERSION; ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('MySQL Version', 'smartshop'); ?></th>
                            <td><?php echo $GLOBALS['wpdb']->db_version(); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Server Software', 'smartshop'); ?></th>
                            <td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Memory Limit', 'smartshop'); ?></th>
                            <td><?php echo ini_get('memory_limit'); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Max Upload Size', 'smartshop'); ?></th>
                            <td><?php echo size_format(wp_max_upload_size()); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Database Tables', 'smartshop'); ?></th>
                            <td>
                                <?php if (SmartShop_Database::tables_exist()): ?>
                                    <span class="status-indicator success">✓</span> <?php _e('All tables exist', 'smartshop'); ?>
                                <?php else: ?>
                                    <span class="status-indicator error">✗</span> <?php _e('Some tables missing', 'smartshop'); ?>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th><?php _e('Total Products', 'smartshop'); ?></th>
                            <td><?php echo SmartShop_Product::get_product_count(); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Total Orders', 'smartshop'); ?></th>
                            <td><?php echo SmartShop_Order::get_order_count(); ?></td>
                        </tr>
                        <tr>
                            <th><?php _e('Shop URL', 'smartshop'); ?></th>
                            <td>
                                <a href="<?php echo SmartShop_Public::get_shop_url(); ?>" target="_blank">
                                    <?php echo SmartShop_Public::get_shop_url(); ?>
                                </a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Debug Information -->
        <div class="smartshop-card">
            <div class="smartshop-card-header">
                <h2 class="smartshop-card-title"><?php _e('Debug Information', 'smartshop'); ?></h2>
                <p class="smartshop-card-description">
                    <?php _e('Debug information for developers and support.', 'smartshop'); ?>
                </p>
            </div>
            
            <div class="debug-info">
                <h4><?php _e('Active Plugins', 'smartshop'); ?></h4>
                <ul class="plugin-list">
                    <?php
                    $active_plugins = get_option('active_plugins');
                    foreach ($active_plugins as $plugin) {
                        $plugin_data = get_plugin_data(WP_PLUGIN_DIR . '/' . $plugin);
                        echo '<li>' . esc_html($plugin_data['Name']) . ' v' . esc_html($plugin_data['Version']) . '</li>';
                    }
                    ?>
                </ul>
                
                <h4><?php _e('Theme Information', 'smartshop'); ?></h4>
                <p>
                    <strong><?php _e('Active Theme:', 'smartshop'); ?></strong> 
                    <?php echo wp_get_theme()->get('Name'); ?> v<?php echo wp_get_theme()->get('Version'); ?>
                </p>
                
                <h4><?php _e('WordPress Constants', 'smartshop'); ?></h4>
                <ul class="constants-list">
                    <li><strong>WP_DEBUG:</strong> <?php echo defined('WP_DEBUG') && WP_DEBUG ? 'Enabled' : 'Disabled'; ?></li>
                    <li><strong>WP_DEBUG_LOG:</strong> <?php echo defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'Enabled' : 'Disabled'; ?></li>
                    <li><strong>WP_CACHE:</strong> <?php echo defined('WP_CACHE') && WP_CACHE ? 'Enabled' : 'Disabled'; ?></li>
                    <li><strong>MULTISITE:</strong> <?php echo is_multisite() ? 'Yes' : 'No'; ?></li>
                </ul>
            </div>
        </div>
        
    </div>
</div>

<style>
.smartshop-tools-container {
    display: grid;
    gap: 20px;
    margin-top: 20px;
}

.tool-section {
    padding: 20px 0;
    border-bottom: 1px solid #f0f0f1;
}

.tool-section:last-child {
    border-bottom: none;
}

.tool-section h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
}

.tool-section p {
    margin: 0 0 15px 0;
    color: #646970;
}

.warning-text {
    color: #d63638 !important;
    font-weight: 500;
}

.file-upload-section {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.file-upload-section input[type="file"] {
    flex: 1;
    max-width: 300px;
}

.import-help {
    background: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.import-help h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
}

.import-help code {
    display: block;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 10px;
    font-size: 12px;
    line-height: 1.4;
    overflow-x: auto;
}

.system-info .smartshop-table th {
    width: 200px;
    font-weight: 600;
}

.status-indicator {
    font-weight: bold;
}

.status-indicator.success {
    color: #00a32a;
}

.status-indicator.error {
    color: #d63638;
}

.plugin-list,
.constants-list {
    list-style: disc;
    margin-left: 20px;
}

.plugin-list li,
.constants-list li {
    margin-bottom: 5px;
    font-size: 14px;
}

.debug-info h4 {
    margin: 20px 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #f0f0f1;
    padding-bottom: 5px;
}

.debug-info h4:first-child {
    margin-top: 0;
}

@media (max-width: 768px) {
    .file-upload-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .file-upload-section input[type="file"] {
        max-width: none;
        margin-bottom: 10px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Handle confirmation dialogs
    $('.smartshop-confirm').on('click', function(e) {
        var message = $(this).data('confirm');
        if (!confirm(message)) {
            e.preventDefault();
            return false;
        }
    });
    
    // File upload validation
    $('input[type="file"]').on('change', function() {
        var file = this.files[0];
        if (file) {
            var fileType = file.type;
            var fileName = file.name;
            
            if (fileType !== 'text/csv' && !fileName.endsWith('.csv')) {
                alert('Please select a valid CSV file.');
                $(this).val('');
                return false;
            }
            
            // Check file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('File size must be less than 5MB.');
                $(this).val('');
                return false;
            }
        }
    });
});
</script>
