/* SmartShop Admin Styles */

/* General <PERSON><PERSON> Styles */
.smartshop-admin-page {
    background: #f1f1f1;
}

.smartshop-admin-header {
    background: #fff;
    border-bottom: 1px solid #c3c4c7;
    padding: 20px 0;
    margin-bottom: 20px;
}

.smartshop-admin-title {
    font-size: 23px;
    font-weight: 400;
    margin: 0;
    color: #23282d;
}

/* Form Styles */
.smartshop-form-table {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    margin-bottom: 20px;
}

.smartshop-form-table th {
    background: #f9f9f9;
    border-bottom: 1px solid #c3c4c7;
    padding: 15px 20px;
    font-weight: 600;
    width: 200px;
    vertical-align: top;
}

.smartshop-form-table td {
    padding: 15px 20px;
    border-bottom: 1px solid #c3c4c7;
}

.smartshop-form-table tr:last-child th,
.smartshop-form-table tr:last-child td {
    border-bottom: none;
}

.smartshop-form-field {
    margin-bottom: 15px;
}

.smartshop-form-field:last-child {
    margin-bottom: 0;
}

.smartshop-form-label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    color: #23282d;
}

.smartshop-form-input {
    width: 100%;
    max-width: 400px;
    padding: 8px 12px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
    background: #fff;
    color: #2c3338;
}

.smartshop-form-input:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

.smartshop-form-textarea {
    width: 100%;
    max-width: 600px;
    min-height: 100px;
    padding: 8px 12px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
    background: #fff;
    color: #2c3338;
    resize: vertical;
}

.smartshop-form-select {
    width: 100%;
    max-width: 400px;
    padding: 8px 12px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
    font-size: 14px;
    background: #fff;
    color: #2c3338;
}

.smartshop-form-checkbox {
    margin-right: 8px;
}

.smartshop-form-description {
    font-size: 13px;
    color: #646970;
    margin-top: 5px;
    font-style: italic;
}

/* Color Picker */
.smartshop-color-picker {
    display: flex;
    align-items: center;
    gap: 10px;
}

.smartshop-color-preview {
    width: 30px;
    height: 30px;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    cursor: pointer;
}

/* Image Upload */
.smartshop-image-upload {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.smartshop-image-preview {
    width: 150px;
    height: 150px;
    border: 2px dashed #c3c4c7;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f9f9f9;
    position: relative;
    overflow: hidden;
}

.smartshop-image-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
}

.smartshop-image-placeholder {
    text-align: center;
    color: #646970;
    font-size: 13px;
}

.smartshop-image-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Product Gallery */
.smartshop-gallery {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.smartshop-gallery-item {
    position: relative;
    width: 100px;
    height: 100px;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    overflow: hidden;
}

.smartshop-gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.smartshop-gallery-remove {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #d63638;
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Buttons */
.smartshop-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.smartshop-btn-primary {
    background: #2271b1;
    border-color: #2271b1;
    color: #fff;
}

.smartshop-btn-primary:hover {
    background: #135e96;
    border-color: #135e96;
    color: #fff;
}

.smartshop-btn-secondary {
    background: #f6f7f7;
    border-color: #c3c4c7;
    color: #2c3338;
}

.smartshop-btn-secondary:hover {
    background: #f0f0f1;
    border-color: #8c8f94;
    color: #2c3338;
}

.smartshop-btn-danger {
    background: #d63638;
    border-color: #d63638;
    color: #fff;
}

.smartshop-btn-danger:hover {
    background: #b32d2e;
    border-color: #b32d2e;
    color: #fff;
}

.smartshop-btn-small {
    padding: 4px 8px;
    font-size: 12px;
}

.smartshop-btn-large {
    padding: 12px 24px;
    font-size: 16px;
}

/* Tables */
.smartshop-table {
    width: 100%;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    border-collapse: collapse;
}

.smartshop-table th {
    background: #f9f9f9;
    border-bottom: 1px solid #c3c4c7;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #23282d;
}

.smartshop-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f1;
    vertical-align: top;
}

.smartshop-table tr:last-child td {
    border-bottom: none;
}

.smartshop-table tr:hover {
    background: #f9f9f9;
}

/* Status Badges */
.smartshop-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.smartshop-status-active,
.smartshop-status-publish,
.smartshop-status-completed {
    background: #d4edda;
    color: #155724;
}

.smartshop-status-inactive,
.smartshop-status-draft,
.smartshop-status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.smartshop-status-pending,
.smartshop-status-processing {
    background: #fff3cd;
    color: #856404;
}

.smartshop-status-outofstock {
    background: #f8d7da;
    color: #721c24;
}

.smartshop-status-instock {
    background: #d4edda;
    color: #155724;
}

/* Cards */
.smartshop-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.smartshop-card-header {
    border-bottom: 1px solid #f0f0f1;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.smartshop-card-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    color: #23282d;
}

.smartshop-card-description {
    font-size: 14px;
    color: #646970;
    margin: 5px 0 0 0;
}

/* Tabs */
.smartshop-tabs {
    border-bottom: 1px solid #c3c4c7;
    margin-bottom: 20px;
}

.smartshop-tabs-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.smartshop-tabs-nav li {
    margin-right: 5px;
}

.smartshop-tabs-nav a {
    display: block;
    padding: 12px 20px;
    text-decoration: none;
    color: #646970;
    border: 1px solid transparent;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    transition: all 0.2s ease;
}

.smartshop-tabs-nav a:hover {
    background: #f9f9f9;
    color: #2c3338;
}

.smartshop-tabs-nav a.active {
    background: #fff;
    color: #2c3338;
    border-color: #c3c4c7;
    border-bottom-color: #fff;
    margin-bottom: -1px;
}

.smartshop-tab-content {
    display: none;
}

.smartshop-tab-content.active {
    display: block;
}

/* Notices */
.smartshop-notice {
    padding: 12px 15px;
    border-left: 4px solid;
    background: #fff;
    margin: 15px 0;
    border-radius: 0 4px 4px 0;
}

.smartshop-notice-success {
    border-left-color: #00a32a;
    background: #f0f6fc;
}

.smartshop-notice-error {
    border-left-color: #d63638;
    background: #fcf0f1;
}

.smartshop-notice-warning {
    border-left-color: #dba617;
    background: #fcf9e8;
}

.smartshop-notice-info {
    border-left-color: #72aee6;
    background: #f0f6fc;
}

/* Loading States */
.smartshop-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.smartshop-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: smartshop-spin 1s linear infinite;
}

@keyframes smartshop-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .smartshop-form-input,
    .smartshop-form-textarea,
    .smartshop-form-select {
        max-width: 100%;
    }
    
    .smartshop-image-upload {
        flex-direction: column;
    }
    
    .smartshop-tabs-nav {
        flex-wrap: wrap;
    }
    
    .smartshop-tabs-nav li {
        margin-right: 0;
        margin-bottom: 5px;
        flex: 1;
    }
    
    .smartshop-tabs-nav a {
        text-align: center;
    }
}

/* WordPress Admin Compatibility */
.wp-admin .smartshop-form-table {
    border-collapse: separate;
    border-spacing: 0;
}

.wp-admin .smartshop-btn {
    vertical-align: top;
}

.wp-admin .smartshop-notice {
    margin: 5px 0 15px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .smartshop-card,
    .smartshop-form-table {
        background: #1d2327;
        border-color: #3c434a;
    }
    
    .smartshop-form-table th {
        background: #23282d;
        border-color: #3c434a;
    }
    
    .smartshop-form-input,
    .smartshop-form-textarea,
    .smartshop-form-select {
        background: #1d2327;
        border-color: #3c434a;
        color: #f0f0f1;
    }
    
    .smartshop-admin-title,
    .smartshop-card-title,
    .smartshop-form-label {
        color: #f0f0f1;
    }
    
    .smartshop-form-description {
        color: #a7aaad;
    }
}
