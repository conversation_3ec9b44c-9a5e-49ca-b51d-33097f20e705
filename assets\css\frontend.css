/* SmartShop Frontend Styles */

/* CSS Variables for Customization */
:root {
  --smartshop-primary-color: #3b82f6;
  --smartshop-secondary-color: #1e40af;
  --smartshop-accent-color: #f59e0b;
  --smartshop-success-color: #10b981;
  --smartshop-error-color: #ef4444;
  --smartshop-warning-color: #f59e0b;
  --smartshop-text-color: #374151;
  --smartshop-text-light: #6b7280;
  --smartshop-border-color: #e5e7eb;
  --smartshop-bg-color: #ffffff;
  --smartshop-bg-light: #f9fafb;
  --smartshop-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --smartshop-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --smartshop-border-radius: 0.375rem;
  --smartshop-border-radius-lg: 0.5rem;
  --smartshop-transition: all 0.2s ease-in-out;
}

/* Reset and Base Styles */
.smartshop-page * {
  box-sizing: border-box;
}

.smartshop-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: var(--smartshop-text-color);
}

/* Container */
.smartshop-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Grid System */
.smartshop-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -0.75rem;
}

.smartshop-col {
  padding: 0 0.75rem;
  flex: 1;
}

.smartshop-col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.smartshop-col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.smartshop-col-3 { flex: 0 0 25%; max-width: 25%; }
.smartshop-col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.smartshop-col-6 { flex: 0 0 50%; max-width: 50%; }
.smartshop-col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.smartshop-col-9 { flex: 0 0 75%; max-width: 75%; }
.smartshop-col-12 { flex: 0 0 100%; max-width: 100%; }

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--smartshop-border-radius);
  cursor: pointer;
  transition: var(--smartshop-transition);
  text-align: center;
  white-space: nowrap;
  user-select: none;
}

.btn:hover {
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--smartshop-primary-color);
  border-color: var(--smartshop-primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--smartshop-secondary-color);
  border-color: var(--smartshop-secondary-color);
  color: white;
}

.btn-secondary {
  background-color: var(--smartshop-bg-light);
  border-color: var(--smartshop-border-color);
  color: var(--smartshop-text-color);
}

.btn-secondary:hover {
  background-color: var(--smartshop-border-color);
  color: var(--smartshop-text-color);
}

.btn-outline {
  background-color: transparent;
  border-color: var(--smartshop-primary-color);
  color: var(--smartshop-primary-color);
}

.btn-outline:hover {
  background-color: var(--smartshop-primary-color);
  color: white;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

/* Product Grid */
.smartshop-product-grid {
  display: grid;
  gap: 1.5rem;
  margin: 2rem 0;
}

.smartshop-columns-2 {
  grid-template-columns: repeat(2, 1fr);
}

.smartshop-columns-3 {
  grid-template-columns: repeat(3, 1fr);
}

.smartshop-columns-4 {
  grid-template-columns: repeat(4, 1fr);
}

.smartshop-columns-5 {
  grid-template-columns: repeat(5, 1fr);
}

/* Product Card */
.smartshop-product-card {
  background: var(--smartshop-bg-color);
  border: 1px solid var(--smartshop-border-color);
  border-radius: var(--smartshop-border-radius-lg);
  overflow: hidden;
  transition: var(--smartshop-transition);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.smartshop-product-card:hover {
  box-shadow: var(--smartshop-shadow-lg);
  transform: translateY(-2px);
}

.smartshop-product-card .product-image {
  position: relative;
  overflow: hidden;
  aspect-ratio: 1;
}

.smartshop-product-card .product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--smartshop-transition);
}

.smartshop-product-card:hover .product-image img {
  transform: scale(1.05);
}

.smartshop-product-image-placeholder {
  width: 100%;
  height: 100%;
  background-color: var(--smartshop-bg-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--smartshop-text-light);
  font-size: 0.875rem;
}

.product-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: var(--smartshop-border-radius);
  color: white;
  z-index: 1;
}

.product-badge.featured {
  background-color: var(--smartshop-accent-color);
}

.product-badge.sale {
  background-color: var(--smartshop-error-color);
}

.smartshop-product-card .product-info {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.smartshop-product-card .product-title {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.4;
}

.smartshop-product-card .product-title a {
  color: var(--smartshop-text-color);
  text-decoration: none;
  transition: var(--smartshop-transition);
}

.smartshop-product-card .product-title a:hover {
  color: var(--smartshop-primary-color);
}

.smartshop-product-card .product-category {
  font-size: 0.75rem;
  color: var(--smartshop-text-light);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.smartshop-product-card .product-price {
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.smartshop-price {
  color: var(--smartshop-text-color);
  font-size: 1.125rem;
}

.smartshop-price-sale {
  color: var(--smartshop-error-color);
  font-size: 1.125rem;
}

.smartshop-price-regular {
  color: var(--smartshop-text-light);
  font-size: 0.875rem;
  text-decoration: line-through;
  margin-left: 0.5rem;
}

.smartshop-product-card .product-stock {
  margin-bottom: 1rem;
  font-size: 0.75rem;
}

.smartshop-stock-status {
  padding: 0.125rem 0.375rem;
  border-radius: var(--smartshop-border-radius);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.smartshop-stock-status.in-stock {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--smartshop-success-color);
}

.smartshop-stock-status.low-stock {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--smartshop-warning-color);
}

.smartshop-stock-status.out-of-stock {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--smartshop-error-color);
}

.smartshop-product-card .product-actions {
  margin-top: auto;
  display: flex;
  gap: 0.5rem;
}

.smartshop-product-card .product-actions .btn {
  flex: 1;
  font-size: 0.75rem;
  padding: 0.5rem;
}

/* Search Form */
.smartshop-search-form {
  margin-bottom: 2rem;
}

.search-input-group {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.search-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--smartshop-border-color);
  border-radius: var(--smartshop-border-radius);
  font-size: 0.875rem;
  transition: var(--smartshop-transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--smartshop-primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-category {
  padding: 0.75rem;
  border: 1px solid var(--smartshop-border-color);
  border-radius: var(--smartshop-border-radius);
  font-size: 0.875rem;
  background-color: var(--smartshop-bg-color);
  min-width: 150px;
}

.search-button {
  padding: 0.75rem 1.5rem;
  background-color: var(--smartshop-primary-color);
  color: white;
  border: none;
  border-radius: var(--smartshop-border-radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--smartshop-transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.search-button:hover {
  background-color: var(--smartshop-secondary-color);
}

/* Filters Sidebar */
.smartshop-filters-sidebar {
  background: var(--smartshop-bg-color);
  border: 1px solid var(--smartshop-border-color);
  border-radius: var(--smartshop-border-radius-lg);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.filters-title {
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--smartshop-text-color);
}

.filter-group {
  margin-bottom: 2rem;
}

.filter-group:last-child {
  margin-bottom: 0;
}

.filter-title {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--smartshop-text-color);
}

.filter-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.filter-item {
  margin-bottom: 0.5rem;
}

.filter-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.875rem;
  transition: var(--smartshop-transition);
}

.filter-label:hover {
  color: var(--smartshop-primary-color);
}

.filter-checkbox {
  margin-right: 0.5rem;
  accent-color: var(--smartshop-primary-color);
}

.filter-count {
  margin-left: auto;
  color: var(--smartshop-text-light);
  font-size: 0.75rem;
}

.price-filter {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.price-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.price-input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid var(--smartshop-border-color);
  border-radius: var(--smartshop-border-radius);
  font-size: 0.875rem;
}

.price-separator {
  color: var(--smartshop-text-light);
  font-weight: 500;
}

/* Pagination */
.smartshop-pagination {
  margin: 2rem 0;
  display: flex;
  justify-content: center;
}

.pagination-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0.25rem;
}

.pagination-item {
  display: flex;
}

.pagination-link {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  text-decoration: none;
  border: 1px solid var(--smartshop-border-color);
  border-radius: var(--smartshop-border-radius);
  color: var(--smartshop-text-color);
  transition: var(--smartshop-transition);
  min-width: 2.5rem;
}

.pagination-link:hover {
  background-color: var(--smartshop-bg-light);
  text-decoration: none;
}

.pagination-item.active .pagination-link {
  background-color: var(--smartshop-primary-color);
  border-color: var(--smartshop-primary-color);
  color: white;
}

.pagination-item.dots span {
  padding: 0.5rem 0.75rem;
  color: var(--smartshop-text-light);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .smartshop-columns-4 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .smartshop-columns-5 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .smartshop-container {
    padding: 0 0.5rem;
  }
  
  .smartshop-columns-3,
  .smartshop-columns-4,
  .smartshop-columns-5 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .smartshop-row {
    margin: 0 -0.5rem;
  }
  
  .smartshop-col {
    padding: 0 0.5rem;
  }
  
  .search-input-group {
    flex-direction: column;
  }
  
  .search-category {
    min-width: auto;
    width: 100%;
  }
  
  .smartshop-product-card .product-actions {
    flex-direction: column;
  }
  
  .pagination-link {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
    min-width: 2rem;
  }
}

@media (max-width: 480px) {
  .smartshop-columns-2,
  .smartshop-columns-3,
  .smartshop-columns-4,
  .smartshop-columns-5 {
    grid-template-columns: 1fr;
  }
  
  .smartshop-product-grid {
    gap: 1rem;
  }
  
  .smartshop-filters-sidebar {
    padding: 1rem;
  }
}

/* Cart Modal */
.smartshop-cart-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: none;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.smartshop-cart-modal.active {
  display: flex;
}

.cart-modal-content {
  background: var(--smartshop-bg-color);
  border-radius: var(--smartshop-border-radius-lg);
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: var(--smartshop-shadow-lg);
}

.cart-modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--smartshop-border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.cart-modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.cart-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--smartshop-text-light);
  padding: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--smartshop-border-radius);
  transition: var(--smartshop-transition);
}

.cart-modal-close:hover {
  background-color: var(--smartshop-bg-light);
  color: var(--smartshop-text-color);
}

.cart-modal-body {
  padding: 1.5rem;
}

.cart-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid var(--smartshop-border-color);
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item-image {
  width: 60px;
  height: 60px;
  border-radius: var(--smartshop-border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.cart-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cart-item-details {
  flex: 1;
}

.cart-item-name {
  font-weight: 600;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.cart-item-price {
  color: var(--smartshop-text-light);
  font-size: 0.75rem;
}

.cart-item-quantity {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.quantity-btn {
  width: 1.5rem;
  height: 1.5rem;
  border: 1px solid var(--smartshop-border-color);
  background: var(--smartshop-bg-color);
  border-radius: var(--smartshop-border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.75rem;
  transition: var(--smartshop-transition);
}

.quantity-btn:hover {
  background-color: var(--smartshop-bg-light);
}

.quantity-input {
  width: 3rem;
  text-align: center;
  border: 1px solid var(--smartshop-border-color);
  border-radius: var(--smartshop-border-radius);
  padding: 0.25rem;
  font-size: 0.75rem;
}

.cart-item-remove {
  background: none;
  border: none;
  color: var(--smartshop-error-color);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--smartshop-border-radius);
  transition: var(--smartshop-transition);
}

.cart-item-remove:hover {
  background-color: rgba(239, 68, 68, 0.1);
}

.cart-modal-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--smartshop-border-color);
}

.cart-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 1.125rem;
  font-weight: 600;
}

.cart-actions {
  display: flex;
  gap: 0.5rem;
}

.cart-actions .btn {
  flex: 1;
}

/* Notifications */
.smartshop-notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: var(--smartshop-bg-color);
  border: 1px solid var(--smartshop-border-color);
  border-radius: var(--smartshop-border-radius-lg);
  padding: 1rem 1.5rem;
  box-shadow: var(--smartshop-shadow-lg);
  z-index: 10000;
  max-width: 400px;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
}

.smartshop-notification.show {
  transform: translateX(0);
}

.smartshop-notification.success {
  border-left: 4px solid var(--smartshop-success-color);
}

.smartshop-notification.error {
  border-left: 4px solid var(--smartshop-error-color);
}

.smartshop-notification.warning {
  border-left: 4px solid var(--smartshop-warning-color);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.loading {
  opacity: 0.6;
  pointer-events: none;
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
