/**
 * SmartShop Admin JavaScript
 */

(function($) {
    'use strict';

    // SmartShop Admin object
    window.SmartShopAdmin = {
        
        init: function() {
            this.initColorPickers();
            this.initImageUploads();
            this.initTabs();
            this.initFormValidation();
            this.initAjaxForms();
            this.initDataTables();
            this.bindEvents();
        },
        
        bindEvents: function() {
            // Tab navigation
            $(document).on('click', '.smartshop-tabs-nav a', this.handleTabClick);
            
            // Image upload
            $(document).on('click', '.smartshop-upload-btn', this.handleImageUpload);
            $(document).on('click', '.smartshop-remove-image', this.handleImageRemove);
            
            // Gallery management
            $(document).on('click', '.smartshop-gallery-remove', this.handleGalleryRemove);
            
            // Form submission
            $(document).on('submit', '.smartshop-ajax-form', this.handleAjaxForm);
            
            // Bulk actions
            $(document).on('change', '.smartshop-bulk-action', this.handleBulkAction);
            
            // Status updates
            $(document).on('change', '.smartshop-status-select', this.handleStatusUpdate);
            
            // Confirmation dialogs
            $(document).on('click', '.smartshop-confirm', this.handleConfirmation);
        },
        
        initColorPickers: function() {
            if ($.fn.wpColorPicker) {
                $('.smartshop-color-picker input').wpColorPicker({
                    change: function(event, ui) {
                        var color = ui.color.toString();
                        $(this).trigger('colorchange', color);
                    }
                });
            }
        },
        
        initImageUploads: function() {
            // Initialize WordPress media uploader
            this.mediaUploader = null;
        },
        
        initTabs: function() {
            // Show first tab by default
            $('.smartshop-tabs-nav a:first').addClass('active');
            $('.smartshop-tab-content:first').addClass('active');
            
            // Handle hash navigation
            if (window.location.hash) {
                var hash = window.location.hash;
                var $tab = $('.smartshop-tabs-nav a[href="' + hash + '"]');
                if ($tab.length) {
                    $tab.click();
                }
            }
        },
        
        initFormValidation: function() {
            // Add validation to required fields
            $('.smartshop-form-required').each(function() {
                $(this).attr('required', true);
            });
        },
        
        initAjaxForms: function() {
            // Add loading states to AJAX forms
            $('.smartshop-ajax-form').each(function() {
                var $form = $(this);
                var $submit = $form.find('[type="submit"]');
                
                $form.data('original-submit-text', $submit.val() || $submit.text());
            });
        },
        
        initDataTables: function() {
            // Initialize sortable tables
            if ($.fn.DataTable) {
                $('.smartshop-data-table').DataTable({
                    responsive: true,
                    pageLength: 25,
                    order: [[0, 'desc']],
                    language: {
                        search: 'Search:',
                        lengthMenu: 'Show _MENU_ entries',
                        info: 'Showing _START_ to _END_ of _TOTAL_ entries',
                        paginate: {
                            first: 'First',
                            last: 'Last',
                            next: 'Next',
                            previous: 'Previous'
                        }
                    }
                });
            }
        },
        
        handleTabClick: function(e) {
            e.preventDefault();
            
            var $tab = $(this);
            var target = $tab.attr('href');
            
            // Update active states
            $tab.closest('.smartshop-tabs-nav').find('a').removeClass('active');
            $tab.addClass('active');
            
            // Show target content
            $('.smartshop-tab-content').removeClass('active');
            $(target).addClass('active');
            
            // Update URL hash
            if (history.pushState) {
                history.pushState(null, null, target);
            }
        },
        
        handleImageUpload: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $container = $button.closest('.smartshop-image-upload');
            var $preview = $container.find('.smartshop-image-preview');
            var $input = $container.find('input[type="hidden"]');
            var multiple = $button.data('multiple') || false;
            
            // Create media uploader if it doesn't exist
            if (!SmartShopAdmin.mediaUploader) {
                SmartShopAdmin.mediaUploader = wp.media({
                    title: 'Select Image',
                    button: {
                        text: 'Use Image'
                    },
                    multiple: multiple
                });
                
                SmartShopAdmin.mediaUploader.on('select', function() {
                    var selection = SmartShopAdmin.mediaUploader.state().get('selection');
                    
                    if (multiple) {
                        // Handle multiple images (gallery)
                        var images = [];
                        selection.each(function(attachment) {
                            images.push({
                                id: attachment.id,
                                url: attachment.attributes.url,
                                thumbnail: attachment.attributes.sizes.thumbnail ? attachment.attributes.sizes.thumbnail.url : attachment.attributes.url
                            });
                        });
                        
                        SmartShopAdmin.updateGallery($container, images);
                    } else {
                        // Handle single image
                        var attachment = selection.first().toJSON();
                        SmartShopAdmin.updateImagePreview($preview, $input, attachment);
                    }
                });
            }
            
            SmartShopAdmin.mediaUploader.open();
        },
        
        handleImageRemove: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $container = $button.closest('.smartshop-image-upload');
            var $preview = $container.find('.smartshop-image-preview');
            var $input = $container.find('input[type="hidden"]');
            
            // Clear preview and input
            $preview.html('<div class="smartshop-image-placeholder">Click to upload image</div>');
            $input.val('');
        },
        
        handleGalleryRemove: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $item = $button.closest('.smartshop-gallery-item');
            var $container = $button.closest('.smartshop-gallery');
            var $input = $container.siblings('input[type="hidden"]');
            
            // Remove item
            $item.remove();
            
            // Update hidden input
            SmartShopAdmin.updateGalleryInput($container, $input);
        },
        
        handleAjaxForm: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $submit = $form.find('[type="submit"]');
            var originalText = $form.data('original-submit-text');
            
            // Add loading state
            $submit.prop('disabled', true);
            $submit.val ? $submit.val('Saving...') : $submit.text('Saving...');
            $form.addClass('smartshop-loading');
            
            // Submit form via AJAX
            $.ajax({
                url: $form.attr('action') || ajaxurl,
                type: $form.attr('method') || 'POST',
                data: $form.serialize(),
                success: function(response) {
                    if (response.success) {
                        SmartShopAdmin.showNotice('success', response.data.message || 'Saved successfully!');
                        
                        // Trigger custom event
                        $form.trigger('smartshop:form:success', response);
                    } else {
                        SmartShopAdmin.showNotice('error', response.data.message || 'An error occurred.');
                    }
                },
                error: function() {
                    SmartShopAdmin.showNotice('error', 'An error occurred. Please try again.');
                },
                complete: function() {
                    // Remove loading state
                    $submit.prop('disabled', false);
                    $submit.val ? $submit.val(originalText) : $submit.text(originalText);
                    $form.removeClass('smartshop-loading');
                }
            });
        },
        
        handleBulkAction: function() {
            var $select = $(this);
            var action = $select.val();
            var $form = $select.closest('form');
            
            if (action && action !== '-1') {
                var $checked = $form.find('input[type="checkbox"]:checked');
                
                if ($checked.length === 0) {
                    alert('Please select at least one item.');
                    $select.val('-1');
                    return;
                }
                
                if (action === 'delete') {
                    if (!confirm('Are you sure you want to delete the selected items?')) {
                        $select.val('-1');
                        return;
                    }
                }
                
                $form.submit();
            }
        },
        
        handleStatusUpdate: function() {
            var $select = $(this);
            var status = $select.val();
            var itemId = $select.data('item-id');
            var itemType = $select.data('item-type');
            
            if (!itemId || !itemType) {
                return;
            }
            
            // Update status via AJAX
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'smartshop_update_' + itemType + '_status',
                    item_id: itemId,
                    status: status,
                    nonce: $('#smartshop_nonce').val()
                },
                success: function(response) {
                    if (response.success) {
                        SmartShopAdmin.showNotice('success', 'Status updated successfully!');
                    } else {
                        SmartShopAdmin.showNotice('error', response.data.message || 'Failed to update status.');
                    }
                },
                error: function() {
                    SmartShopAdmin.showNotice('error', 'An error occurred. Please try again.');
                }
            });
        },
        
        handleConfirmation: function(e) {
            var $button = $(this);
            var message = $button.data('confirm') || 'Are you sure?';
            
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        },
        
        updateImagePreview: function($preview, $input, attachment) {
            var imageUrl = attachment.sizes && attachment.sizes.medium ? 
                          attachment.sizes.medium.url : attachment.url;
            
            $preview.html('<img src="' + imageUrl + '" alt="">');
            $input.val(attachment.url);
        },
        
        updateGallery: function($container, images) {
            var $gallery = $container.find('.smartshop-gallery');
            var $input = $container.find('input[type="hidden"]');
            
            // Add new images to gallery
            images.forEach(function(image) {
                var $item = $('<div class="smartshop-gallery-item">' +
                             '<img src="' + image.thumbnail + '" alt="">' +
                             '<button type="button" class="smartshop-gallery-remove">&times;</button>' +
                             '</div>');
                $item.data('image-url', image.url);
                $gallery.append($item);
            });
            
            // Update hidden input
            this.updateGalleryInput($gallery, $input);
        },
        
        updateGalleryInput: function($gallery, $input) {
            var urls = [];
            $gallery.find('.smartshop-gallery-item').each(function() {
                var url = $(this).data('image-url') || $(this).find('img').attr('src');
                if (url) {
                    urls.push(url);
                }
            });
            
            $input.val(JSON.stringify(urls));
        },
        
        showNotice: function(type, message) {
            var $notice = $('<div class="smartshop-notice smartshop-notice-' + type + '">' +
                           '<p>' + message + '</p>' +
                           '</div>');
            
            // Remove existing notices
            $('.smartshop-notice').remove();
            
            // Add new notice
            $('.wrap h1').after($notice);
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $notice.remove();
                });
            }, 5000);
            
            // Scroll to notice
            $('html, body').animate({
                scrollTop: $notice.offset().top - 50
            }, 300);
        },
        
        formatCurrency: function(amount) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(amount);
        },
        
        debounce: function(func, wait) {
            var timeout;
            return function executedFunction() {
                var context = this;
                var args = arguments;
                var later = function() {
                    timeout = null;
                    func.apply(context, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        SmartShopAdmin.init();
    });
    
    // Export for global access
    window.SmartShopAdmin = SmartShopAdmin;

})(jQuery);
