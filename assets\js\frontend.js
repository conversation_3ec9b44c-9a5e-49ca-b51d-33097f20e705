/**
 * SmartShop Frontend JavaScript
 *
 * Handles frontend interactions and AJAX requests
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        SmartShop.init();
    });

    // Main SmartShop object
    window.SmartShop = {

        // Configuration
        config: {
            ajaxUrl: smartshop_ajax.ajax_url,
            nonce: smartshop_ajax.nonce,
            currencySymbol: smartshop_ajax.currency_symbol,
            currencyPosition: smartshop_ajax.currency_position,
            cartUrl: smartshop_ajax.cart_url,
            checkoutUrl: smartshop_ajax.checkout_url
        },

        // Initialize all functionality
        init: function() {
            this.bindEvents();
            this.initCart();
            this.initProductFilters();
            this.initQuantityControls();
            this.updateCartCount();
        },

        // Bind event handlers
        bindEvents: function() {
            // Add to cart buttons
            $(document).on('click', '.add-to-cart-btn', this.addToCart);

            // Remove from cart buttons
            $(document).on('click', '.remove-item', this.removeFromCart);

            // Update cart buttons
            $(document).on('click', '.update-cart-btn', this.updateCart);

            // Clear cart button
            $(document).on('click', '.clear-cart-btn', this.clearCart);

            // Quantity controls
            $(document).on('click', '.quantity-plus', this.increaseQuantity);
            $(document).on('click', '.quantity-minus', this.decreaseQuantity);
            $(document).on('change', '.quantity-field', this.updateQuantity);

            // Product filters
            $(document).on('change', '#sort-products', this.sortProducts);
            $(document).on('click', '.filter-price-btn', this.filterByPrice);

            // View mode toggle
            $(document).on('click', '.view-mode button', this.toggleViewMode);

            // Search form
            $(document).on('submit', '.smartshop-search-form', this.searchProducts);

            // Checkout form
            $(document).on('submit', '#smartshop-checkout-form', this.submitOrder);

            // Product tabs
            $(document).on('click', '.tab-button', this.switchTab);

            // Quick view
            $(document).on('click', '.quick-view-btn', this.quickView);
        },
            
            var $button = $(this);
            var productId = $button.data('product-id');
            var quantity = $button.closest('.product-actions').find('.quantity-input').val() || 1;
            
            if ($button.hasClass('disabled') || $button.prop('disabled')) {
                return;
            }
            
            SmartShop.cart.addToCart(productId, quantity, $button);
        },
        
        handleSearch: function(e) {
            var $form = $(this);
            var searchTerm = $form.find('.search-input').val().trim();
            
            if (searchTerm.length < 2) {
                e.preventDefault();
                SmartShop.showNotification(smartshop_frontend.messages.invalid_search, 'warning');
                return;
            }
        },
        
        handleFilterChange: function() {
            SmartShop.applyFilters();
        },
        
        handlePriceFilter: function(e) {
            e.preventDefault();
            SmartShop.applyFilters();
        },
        
        handleClearFilters: function(e) {
            e.preventDefault();
            
            // Clear all filter inputs
            $('.filter-checkbox').prop('checked', false);
            $('.price-input').val('');
            
            // Apply cleared filters
            SmartShop.applyFilters();
        },
        
        handlePagination: function(e) {
            e.preventDefault();
            
            var $link = $(this);
            var url = $link.attr('href');
            
            // Add loading state
            $link.closest('.smartshop-pagination').addClass('loading');
            
            // Load new page content via AJAX
            SmartShop.loadPage(url);
        },
        
        handleQuantityChange: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $input = $button.siblings('.quantity-input');
            var currentValue = parseInt($input.val()) || 1;
            var newValue = currentValue;
            
            if ($button.hasClass('quantity-increase')) {
                newValue = currentValue + 1;
            } else if ($button.hasClass('quantity-decrease') && currentValue > 1) {
                newValue = currentValue - 1;
            }
            
            $input.val(newValue).trigger('change');
        },
        
        handleQuantityInput: function() {
            var $input = $(this);
            var value = parseInt($input.val()) || 1;
            var min = parseInt($input.attr('min')) || 1;
            var max = parseInt($input.attr('max')) || 999;
            
            if (value < min) {
                value = min;
            } else if (value > max) {
                value = max;
            }
            
            $input.val(value);
            
            // Update cart if this is a cart item
            if ($input.closest('.cart-item').length) {
                var productId = $input.closest('.cart-item').data('product-id');
                SmartShop.cart.updateCartItem(productId, value);
            }
        },
        
        handleRemoveFromCart: function(e) {
            e.preventDefault();
            
            if (!confirm(smartshop_frontend.messages.confirm_remove)) {
                return;
            }
            
            var $button = $(this);
            var productId = $button.closest('.cart-item').data('product-id');
            
            SmartShop.cart.removeFromCart(productId);
        },
        
        openCartModal: function() {
            $('.smartshop-cart-modal').addClass('active');
            $('body').addClass('modal-open');
        },
        
        closeCartModal: function(e) {
            if (e.target === e.currentTarget) {
                $('.smartshop-cart-modal').removeClass('active');
                $('body').removeClass('modal-open');
            }
        },
        
        applyFilters: function() {
            var filters = {
                categories: [],
                min_price: $('.min-price').val(),
                max_price: $('.max-price').val(),
                in_stock: $('.filter-checkbox[name="in_stock"]').is(':checked') ? 1 : 0
            };
            
            // Get selected categories
            $('.categories-filter .filter-checkbox:checked').each(function() {
                filters.categories.push($(this).val());
            });
            
            // Build URL with filters
            var url = new URL(window.location.href);
            
            // Clear existing filter params
            url.searchParams.delete('categories');
            url.searchParams.delete('min_price');
            url.searchParams.delete('max_price');
            url.searchParams.delete('in_stock');
            
            // Add new filter params
            if (filters.categories.length > 0) {
                url.searchParams.set('categories', filters.categories.join(','));
            }
            if (filters.min_price) {
                url.searchParams.set('min_price', filters.min_price);
            }
            if (filters.max_price) {
                url.searchParams.set('max_price', filters.max_price);
            }
            if (filters.in_stock) {
                url.searchParams.set('in_stock', '1');
            }
            
            // Load filtered results
            SmartShop.loadPage(url.toString());
        },
        
        loadPage: function(url) {
            // Show loading state
            $('.smartshop-product-grid').addClass('loading');
            
            // Load content via AJAX
            $.get(url)
                .done(function(data) {
                    // Extract product grid from response
                    var $newContent = $(data).find('.smartshop-product-grid');
                    var $newPagination = $(data).find('.smartshop-pagination');
                    
                    if ($newContent.length) {
                        $('.smartshop-product-grid').replaceWith($newContent);
                    }
                    
                    if ($newPagination.length) {
                        $('.smartshop-pagination').replaceWith($newPagination);
                    }
                    
                    // Update URL without page reload
                    history.pushState(null, '', url);
                    
                    // Scroll to top of products
                    $('html, body').animate({
                        scrollTop: $('.smartshop-product-grid').offset().top - 100
                    }, 300);
                })
                .fail(function() {
                    SmartShop.showNotification(smartshop_frontend.messages.error, 'error');
                })
                .always(function() {
                    $('.smartshop-product-grid, .smartshop-pagination').removeClass('loading');
                });
        },
        
        initFilters: function() {
            // Set filter values from URL params
            var urlParams = new URLSearchParams(window.location.search);
            
            // Set category filters
            var categories = urlParams.get('categories');
            if (categories) {
                categories.split(',').forEach(function(categoryId) {
                    $('.categories-filter .filter-checkbox[value="' + categoryId + '"]').prop('checked', true);
                });
            }
            
            // Set price filters
            if (urlParams.get('min_price')) {
                $('.min-price').val(urlParams.get('min_price'));
            }
            if (urlParams.get('max_price')) {
                $('.max-price').val(urlParams.get('max_price'));
            }
            
            // Set stock filter
            if (urlParams.get('in_stock')) {
                $('.filter-checkbox[name="in_stock"]').prop('checked', true);
            }
        },
        
        initLazyLoading: function() {
            if ('IntersectionObserver' in window) {
                var imageObserver = new IntersectionObserver(function(entries, observer) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            var img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    });
                });
                
                document.querySelectorAll('img[data-src]').forEach(function(img) {
                    imageObserver.observe(img);
                });
            }
        },
        
        showNotification: function(message, type) {
            type = type || 'success';
            
            var $notification = $('<div class="smartshop-notification ' + type + '">' + message + '</div>');
            $('body').append($notification);
            
            // Show notification
            setTimeout(function() {
                $notification.addClass('show');
            }, 100);
            
            // Auto hide after 5 seconds
            setTimeout(function() {
                SmartShop.closeNotification.call($notification[0]);
            }, 5000);
        },
        
        closeNotification: function() {
            var $notification = $(this);
            $notification.removeClass('show');
            
            setTimeout(function() {
                $notification.remove();
            }, 300);
        },
        
        formatCurrency: function(amount) {
            var symbol = smartshop_frontend.currency_symbol;
            var position = smartshop_frontend.currency_position;
            var formatted = parseFloat(amount).toFixed(2);
            
            if (position === 'left') {
                return symbol + formatted;
            } else {
                return formatted + symbol;
            }
        }
    };
    
    // SmartShop Cart Class
    function SmartShopCart() {
        this.items = [];
        this.total = 0;
        this.count = 0;
        
        this.init();
    }
    
    SmartShopCart.prototype = {
        init: function() {
            this.loadCart();
        },
        
        addToCart: function(productId, quantity, $button) {
            var self = this;
            quantity = parseInt(quantity) || 1;
            
            // Add loading state
            $button.addClass('loading').prop('disabled', true);
            var originalText = $button.text();
            $button.text(smartshop_frontend.messages.loading);
            
            $.ajax({
                url: smartshop_frontend.ajax_url,
                type: 'POST',
                data: {
                    action: 'smartshop_add_to_cart',
                    nonce: smartshop_frontend.nonce,
                    product_id: productId,
                    quantity: quantity
                },
                success: function(response) {
                    if (response.success) {
                        self.updateCartDisplay(response.data);
                        SmartShop.showNotification(response.data.message, 'success');
                        
                        // Show cart modal if enabled
                        if (smartshop_frontend.enable_ajax_cart && !smartshop_frontend.cart_redirect_after_add) {
                            SmartShop.openCartModal();
                        }
                        
                        // Redirect to cart if enabled
                        if (smartshop_frontend.cart_redirect_after_add) {
                            window.location.href = smartshop_frontend.cart_url;
                        }
                    } else {
                        SmartShop.showNotification(response.data.message, 'error');
                    }
                },
                error: function() {
                    SmartShop.showNotification(smartshop_frontend.messages.error, 'error');
                },
                complete: function() {
                    $button.removeClass('loading').prop('disabled', false).text(originalText);
                }
            });
        },
        
        updateCartItem: function(productId, quantity) {
            var self = this;
            
            $.ajax({
                url: smartshop_frontend.ajax_url,
                type: 'POST',
                data: {
                    action: 'smartshop_update_cart',
                    nonce: smartshop_frontend.nonce,
                    product_id: productId,
                    quantity: quantity
                },
                success: function(response) {
                    if (response.success) {
                        self.updateCartDisplay(response.data);
                        self.updateCartModal(response.data.cart_items);
                    } else {
                        SmartShop.showNotification(response.data.message, 'error');
                    }
                },
                error: function() {
                    SmartShop.showNotification(smartshop_frontend.messages.error, 'error');
                }
            });
        },
        
        removeFromCart: function(productId) {
            var self = this;
            
            $.ajax({
                url: smartshop_frontend.ajax_url,
                type: 'POST',
                data: {
                    action: 'smartshop_remove_from_cart',
                    nonce: smartshop_frontend.nonce,
                    product_id: productId
                },
                success: function(response) {
                    if (response.success) {
                        self.updateCartDisplay(response.data);
                        self.updateCartModal(response.data.cart_items);
                        SmartShop.showNotification(response.data.message, 'success');
                    } else {
                        SmartShop.showNotification(response.data.message, 'error');
                    }
                },
                error: function() {
                    SmartShop.showNotification(smartshop_frontend.messages.error, 'error');
                }
            });
        },
        
        loadCart: function() {
            var self = this;
            
            $.ajax({
                url: smartshop_frontend.ajax_url,
                type: 'POST',
                data: {
                    action: 'smartshop_get_cart',
                    nonce: smartshop_frontend.nonce
                },
                success: function(response) {
                    if (response.success) {
                        self.updateCartDisplay(response.data);
                    }
                }
            });
        },
        
        updateCartDisplay: function(data) {
            if (data) {
                this.count = data.cart_count || 0;
                this.total = data.cart_total || SmartShop.formatCurrency(0);
            }
            
            // Update cart count in header/navigation
            $('.cart-count').text(this.count);
            $('.cart-total').text(this.total);
            
            // Update cart badge
            if (this.count > 0) {
                $('.cart-badge').text(this.count).show();
            } else {
                $('.cart-badge').hide();
            }
        },
        
        updateCartModal: function(items) {
            var $modalBody = $('.cart-modal-body');
            
            if (!items || items.length === 0) {
                $modalBody.html('<p class="text-center">' + smartshop_frontend.messages.empty_cart + '</p>');
                return;
            }
            
            var html = '';
            items.forEach(function(item) {
                html += '<div class="cart-item" data-product-id="' + item.product_id + '">';
                html += '<div class="cart-item-image">';
                html += '<img src="' + (item.product.image_url || '') + '" alt="' + item.product.name + '">';
                html += '</div>';
                html += '<div class="cart-item-details">';
                html += '<div class="cart-item-name">' + item.product.name + '</div>';
                html += '<div class="cart-item-price">' + SmartShop.formatCurrency(item.price) + '</div>';
                html += '<div class="cart-item-quantity">';
                html += '<button class="quantity-btn quantity-decrease">-</button>';
                html += '<input type="number" class="quantity-input" value="' + item.quantity + '" min="1">';
                html += '<button class="quantity-btn quantity-increase">+</button>';
                html += '</div>';
                html += '</div>';
                html += '<button class="cart-item-remove">×</button>';
                html += '</div>';
            });
            
            $modalBody.html(html);
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        SmartShop.init();
    });

})(jQuery);
