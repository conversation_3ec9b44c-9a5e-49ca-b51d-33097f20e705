<?php
/**
 * SmartShop Debug Helper
 * 
 * Use this file to debug plugin activation issues
 * Upload this file to your WordPress root and access it via browser
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('./wp-load.php');

echo '<h1>SmartShop Plugin Debug</h1>';

// Check PHP version
echo '<h2>PHP Version</h2>';
echo '<p>Current: ' . PHP_VERSION . '</p>';
echo '<p>Required: 7.4+</p>';
echo '<p>Status: ' . (version_compare(PHP_VERSION, '7.4.0', '>=') ? '✅ OK' : '❌ FAIL') . '</p>';

// Check WordPress version
echo '<h2>WordPress Version</h2>';
echo '<p>Current: ' . get_bloginfo('version') . '</p>';
echo '<p>Required: 5.0+</p>';
echo '<p>Status: ' . (version_compare(get_bloginfo('version'), '5.0', '>=') ? '✅ OK' : '❌ FAIL') . '</p>';

// Check if plugin directory exists
echo '<h2>Plugin Directory</h2>';
$plugin_dir = WP_PLUGIN_DIR . '/smartshop';
echo '<p>Path: ' . $plugin_dir . '</p>';
echo '<p>Exists: ' . (is_dir($plugin_dir) ? '✅ YES' : '❌ NO') . '</p>';

if (is_dir($plugin_dir)) {
    echo '<h3>Plugin Files</h3>';
    $required_files = array(
        'smartshop.php',
        'includes/class-smartshop-database.php',
        'includes/class-smartshop-settings.php',
        'includes/class-smartshop-product.php',
        'includes/class-smartshop-cart.php',
        'includes/class-smartshop-order.php',
        'includes/class-smartshop-shortcodes.php',
        'admin/class-smartshop-admin.php',
        'public/class-smartshop-public.php',
        'public/class-smartshop-frontend.php'
    );
    
    foreach ($required_files as $file) {
        $file_path = $plugin_dir . '/' . $file;
        echo '<p>' . $file . ': ' . (file_exists($file_path) ? '✅ EXISTS' : '❌ MISSING') . '</p>';
    }
}

// Check database permissions
echo '<h2>Database Permissions</h2>';
global $wpdb;

// Test CREATE TABLE permission
$test_table = $wpdb->prefix . 'smartshop_test';
$create_sql = "CREATE TABLE $test_table (id int(11) NOT NULL AUTO_INCREMENT, test varchar(255), PRIMARY KEY (id))";
$result = $wpdb->query($create_sql);

if ($result !== false) {
    echo '<p>CREATE TABLE: ✅ OK</p>';
    
    // Test INSERT permission
    $insert_result = $wpdb->insert($test_table, array('test' => 'test_value'));
    echo '<p>INSERT: ' . ($insert_result !== false ? '✅ OK' : '❌ FAIL') . '</p>';
    
    // Test SELECT permission
    $select_result = $wpdb->get_var("SELECT test FROM $test_table WHERE id = 1");
    echo '<p>SELECT: ' . ($select_result === 'test_value' ? '✅ OK' : '❌ FAIL') . '</p>';
    
    // Test UPDATE permission
    $update_result = $wpdb->update($test_table, array('test' => 'updated'), array('id' => 1));
    echo '<p>UPDATE: ' . ($update_result !== false ? '✅ OK' : '❌ FAIL') . '</p>';
    
    // Test DELETE permission
    $delete_result = $wpdb->delete($test_table, array('id' => 1));
    echo '<p>DELETE: ' . ($delete_result !== false ? '✅ OK' : '❌ FAIL') . '</p>';
    
    // Clean up test table
    $wpdb->query("DROP TABLE $test_table");
    echo '<p>DROP TABLE: ✅ OK</p>';
} else {
    echo '<p>CREATE TABLE: ❌ FAIL</p>';
    echo '<p>Error: ' . $wpdb->last_error . '</p>';
}

// Check memory limit
echo '<h2>Memory Limit</h2>';
$memory_limit = ini_get('memory_limit');
$memory_bytes = wp_convert_hr_to_bytes($memory_limit);
$required_bytes = wp_convert_hr_to_bytes('128M');
echo '<p>Current: ' . $memory_limit . '</p>';
echo '<p>Required: 128M</p>';
echo '<p>Status: ' . ($memory_bytes >= $required_bytes ? '✅ OK' : '❌ INSUFFICIENT') . '</p>';

// Check if plugin is active
echo '<h2>Plugin Status</h2>';
$active_plugins = get_option('active_plugins');
$plugin_file = 'smartshop/smartshop.php';
echo '<p>Plugin File: ' . $plugin_file . '</p>';
echo '<p>Is Active: ' . (in_array($plugin_file, $active_plugins) ? '✅ YES' : '❌ NO') . '</p>';

// Try to include main plugin file
echo '<h2>Plugin File Test</h2>';
$main_file = $plugin_dir . '/smartshop.php';
if (file_exists($main_file)) {
    echo '<p>Main file exists: ✅ YES</p>';
    
    // Check for syntax errors
    $output = shell_exec("php -l $main_file 2>&1");
    if (strpos($output, 'No syntax errors') !== false) {
        echo '<p>Syntax check: ✅ OK</p>';
    } else {
        echo '<p>Syntax check: ❌ FAIL</p>';
        echo '<pre>' . htmlspecialchars($output) . '</pre>';
    }
} else {
    echo '<p>Main file exists: ❌ NO</p>';
}

// Check for conflicting plugins
echo '<h2>Potential Conflicts</h2>';
$conflicting_plugins = array(
    'woocommerce/woocommerce.php' => 'WooCommerce',
    'easy-digital-downloads/easy-digital-downloads.php' => 'Easy Digital Downloads',
    'wp-ecommerce/wp-shopping-cart.php' => 'WP eCommerce'
);

foreach ($conflicting_plugins as $plugin_path => $plugin_name) {
    if (in_array($plugin_path, $active_plugins)) {
        echo '<p>' . $plugin_name . ': ⚠️ ACTIVE (may conflict)</p>';
    } else {
        echo '<p>' . $plugin_name . ': ✅ NOT ACTIVE</p>';
    }
}

// Check error logs
echo '<h2>Recent Error Logs</h2>';
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    $log_content = file_get_contents($error_log);
    $smartshop_errors = array();
    $lines = explode("\n", $log_content);
    
    foreach ($lines as $line) {
        if (stripos($line, 'smartshop') !== false) {
            $smartshop_errors[] = $line;
        }
    }
    
    if (!empty($smartshop_errors)) {
        echo '<p>SmartShop related errors found:</p>';
        echo '<pre>' . htmlspecialchars(implode("\n", array_slice($smartshop_errors, -10))) . '</pre>';
    } else {
        echo '<p>No SmartShop related errors found in logs.</p>';
    }
} else {
    echo '<p>Error log not accessible or not configured.</p>';
}

echo '<h2>Recommendations</h2>';
echo '<ul>';
echo '<li>If PHP version is below 7.4, upgrade PHP</li>';
echo '<li>If WordPress version is below 5.0, update WordPress</li>';
echo '<li>If database permissions fail, contact your hosting provider</li>';
echo '<li>If memory limit is insufficient, increase it in wp-config.php or .htaccess</li>';
echo '<li>If syntax errors exist, check the plugin files for corruption</li>';
echo '<li>If conflicting plugins are active, temporarily deactivate them</li>';
echo '</ul>';

echo '<p><strong>Delete this debug.php file after troubleshooting!</strong></p>';
?>
