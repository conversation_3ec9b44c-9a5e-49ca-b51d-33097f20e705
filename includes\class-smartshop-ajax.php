<?php
/**
 * SmartShop AJAX Class
 * 
 * Handles AJAX requests
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Ajax {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Cart AJAX actions
        add_action('wp_ajax_smartshop_add_to_cart', array($this, 'add_to_cart'));
        add_action('wp_ajax_nopriv_smartshop_add_to_cart', array($this, 'add_to_cart'));
        
        add_action('wp_ajax_smartshop_update_cart', array($this, 'update_cart'));
        add_action('wp_ajax_nopriv_smartshop_update_cart', array($this, 'update_cart'));
        
        add_action('wp_ajax_smartshop_remove_from_cart', array($this, 'remove_from_cart'));
        add_action('wp_ajax_nopriv_smartshop_remove_from_cart', array($this, 'remove_from_cart'));
        
        add_action('wp_ajax_smartshop_get_cart', array($this, 'get_cart'));
        add_action('wp_ajax_nopriv_smartshop_get_cart', array($this, 'get_cart'));
        
        add_action('wp_ajax_smartshop_clear_cart', array($this, 'clear_cart'));
        add_action('wp_ajax_nopriv_smartshop_clear_cart', array($this, 'clear_cart'));
        
        // Product AJAX actions
        add_action('wp_ajax_smartshop_get_products', array($this, 'get_products'));
        add_action('wp_ajax_nopriv_smartshop_get_products', array($this, 'get_products'));
        
        add_action('wp_ajax_smartshop_search_products', array($this, 'search_products'));
        add_action('wp_ajax_nopriv_smartshop_search_products', array($this, 'search_products'));
        
        // Order AJAX actions
        add_action('wp_ajax_smartshop_create_order', array($this, 'create_order'));
        add_action('wp_ajax_nopriv_smartshop_create_order', array($this, 'create_order'));
    }
    
    /**
     * Add to cart AJAX handler
     */
    public static function add_to_cart() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        $product_id = intval($_POST['product_id'] ?? 0);
        $quantity = intval($_POST['quantity'] ?? 1);
        
        if (!$product_id) {
            wp_send_json_error(array(
                'message' => __('Invalid product ID.', 'smartshop')
            ));
        }
        
        $cart = SmartShop_Cart::get_instance();
        
        if ($cart->add_to_cart($product_id, $quantity)) {
            wp_send_json_success(array(
                'message' => __('Product added to cart successfully!', 'smartshop'),
                'cart_count' => $cart->get_cart_count(),
                'cart_total' => SmartShop_Settings::format_currency($cart->get_cart_total())
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to add product to cart. Please check stock availability.', 'smartshop')
            ));
        }
    }
    
    /**
     * Update cart AJAX handler
     */
    public static function update_cart() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        $product_id = intval($_POST['product_id'] ?? 0);
        $quantity = intval($_POST['quantity'] ?? 0);
        
        if (!$product_id) {
            wp_send_json_error(array(
                'message' => __('Invalid product ID.', 'smartshop')
            ));
        }
        
        $cart = SmartShop_Cart::get_instance();
        
        if ($cart->update_cart_item($product_id, $quantity)) {
            wp_send_json_success(array(
                'message' => __('Cart updated successfully!', 'smartshop'),
                'cart_count' => $cart->get_cart_count(),
                'cart_total' => SmartShop_Settings::format_currency($cart->get_cart_total()),
                'cart_items' => $cart->get_cart_items()
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to update cart.', 'smartshop')
            ));
        }
    }
    
    /**
     * Remove from cart AJAX handler
     */
    public static function remove_from_cart() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        $product_id = intval($_POST['product_id'] ?? 0);
        
        if (!$product_id) {
            wp_send_json_error(array(
                'message' => __('Invalid product ID.', 'smartshop')
            ));
        }
        
        $cart = SmartShop_Cart::get_instance();
        
        if ($cart->remove_from_cart($product_id)) {
            wp_send_json_success(array(
                'message' => __('Product removed from cart!', 'smartshop'),
                'cart_count' => $cart->get_cart_count(),
                'cart_total' => SmartShop_Settings::format_currency($cart->get_cart_total()),
                'cart_items' => $cart->get_cart_items()
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to remove product from cart.', 'smartshop')
            ));
        }
    }
    
    /**
     * Get cart AJAX handler
     */
    public static function get_cart() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        $cart = SmartShop_Cart::get_instance();
        
        wp_send_json_success(array(
            'cart_count' => $cart->get_cart_count(),
            'cart_total' => SmartShop_Settings::format_currency($cart->get_cart_total()),
            'cart_items' => $cart->get_cart_items(),
            'is_empty' => $cart->is_empty()
        ));
    }
    
    /**
     * Clear cart AJAX handler
     */
    public static function clear_cart() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        $cart = SmartShop_Cart::get_instance();
        $cart->clear_cart();
        
        wp_send_json_success(array(
            'message' => __('Cart cleared successfully!', 'smartshop'),
            'cart_count' => 0,
            'cart_total' => SmartShop_Settings::format_currency(0)
        ));
    }
    
    /**
     * Get products AJAX handler
     */
    public static function get_products() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        $args = array(
            'limit' => intval($_POST['limit'] ?? 12),
            'offset' => intval($_POST['offset'] ?? 0),
            'category_id' => intval($_POST['category_id'] ?? 0) ?: null,
            'search' => sanitize_text_field($_POST['search'] ?? ''),
            'orderby' => sanitize_text_field($_POST['orderby'] ?? 'created_at'),
            'order' => sanitize_text_field($_POST['order'] ?? 'DESC'),
            'min_price' => floatval($_POST['min_price'] ?? 0) ?: null,
            'max_price' => floatval($_POST['max_price'] ?? 0) ?: null,
            'in_stock' => isset($_POST['in_stock']) ? (bool)$_POST['in_stock'] : null,
            'featured' => isset($_POST['featured']) ? (bool)$_POST['featured'] : null
        );
        
        $products = SmartShop_Product::get_products($args);
        $total = SmartShop_Product::get_product_count($args);
        
        wp_send_json_success(array(
            'products' => $products,
            'total' => $total,
            'has_more' => ($args['offset'] + $args['limit']) < $total
        ));
    }
    
    /**
     * Search products AJAX handler
     */
    public static function search_products() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        $search = sanitize_text_field($_POST['search'] ?? '');
        $limit = intval($_POST['limit'] ?? 10);
        
        if (empty($search)) {
            wp_send_json_success(array());
        }
        
        $products = SmartShop_Product::get_products(array(
            'search' => $search,
            'limit' => $limit,
            'orderby' => 'name',
            'order' => 'ASC'
        ));
        
        wp_send_json_success($products);
    }
    
    /**
     * Create order AJAX handler
     */
    public static function create_order() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        $cart = SmartShop_Cart::get_instance();
        
        if ($cart->is_empty()) {
            wp_send_json_error(array(
                'message' => __('Your cart is empty.', 'smartshop')
            ));
        }
        
        // Validate required fields
        $required_fields = array('first_name', 'last_name', 'email', 'phone', 'address', 'city');
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                wp_send_json_error(array(
                    'message' => sprintf(__('%s is required.', 'smartshop'), ucfirst(str_replace('_', ' ', $field)))
                ));
            }
        }
        
        // Prepare order data
        $cart_items = $cart->get_cart_items();
        $subtotal = $cart->get_cart_total();
        $tax_total = 0; // Calculate tax if enabled
        $shipping_total = 0; // Calculate shipping if enabled
        $total = $subtotal + $tax_total + $shipping_total;
        
        $order_data = array(
            'subtotal' => $subtotal,
            'tax_total' => $tax_total,
            'shipping_total' => $shipping_total,
            'total' => $total,
            'payment_method' => sanitize_text_field($_POST['payment_method'] ?? 'cod'),
            'billing_data' => array(
                'first_name' => sanitize_text_field($_POST['first_name']),
                'last_name' => sanitize_text_field($_POST['last_name']),
                'email' => sanitize_email($_POST['email']),
                'phone' => sanitize_text_field($_POST['phone']),
                'address' => sanitize_text_field($_POST['address']),
                'city' => sanitize_text_field($_POST['city']),
                'state' => sanitize_text_field($_POST['state'] ?? ''),
                'postcode' => sanitize_text_field($_POST['postcode'] ?? ''),
                'country' => sanitize_text_field($_POST['country'] ?? '')
            ),
            'order_notes' => sanitize_textarea_field($_POST['order_notes'] ?? ''),
            'items' => array()
        );
        
        // Prepare order items
        foreach ($cart_items as $item) {
            $order_data['items'][] = array(
                'product_id' => $item['product_id'],
                'product_name' => $item['product']->name,
                'product_sku' => $item['product']->sku,
                'quantity' => $item['quantity'],
                'price' => $item['price'],
                'total' => $item['total']
            );
        }
        
        $order_id = SmartShop_Order::create_order($order_data);
        
        if ($order_id) {
            // Clear cart
            $cart->clear_cart();
            
            // Trigger order created action
            do_action('smartshop_order_created', $order_id);
            
            wp_send_json_success(array(
                'message' => __('Order placed successfully!', 'smartshop'),
                'order_id' => $order_id,
                'redirect_url' => home_url('/order-confirmation/?order_id=' . $order_id)
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to create order. Please try again.', 'smartshop')
            ));
        }
    }
}
