<?php
/**
 * SmartShop Cart Class
 * 
 * Handles shopping cart operations
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Cart {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Cart session key
     */
    private $cart_session_key = 'smartshop_cart';
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('wp_ajax_smartshop_add_to_cart', array($this, 'ajax_add_to_cart'));
        add_action('wp_ajax_nopriv_smartshop_add_to_cart', array($this, 'ajax_add_to_cart'));
        add_action('wp_ajax_smartshop_update_cart', array($this, 'ajax_update_cart'));
        add_action('wp_ajax_nopriv_smartshop_update_cart', array($this, 'ajax_update_cart'));
        add_action('wp_ajax_smartshop_remove_from_cart', array($this, 'ajax_remove_from_cart'));
        add_action('wp_ajax_nopriv_smartshop_remove_from_cart', array($this, 'ajax_remove_from_cart'));
        add_action('wp_ajax_smartshop_get_cart', array($this, 'ajax_get_cart'));
        add_action('wp_ajax_nopriv_smartshop_get_cart', array($this, 'ajax_get_cart'));
        add_action('wp_ajax_smartshop_clear_cart', array($this, 'ajax_clear_cart'));
        add_action('wp_ajax_nopriv_smartshop_clear_cart', array($this, 'ajax_clear_cart'));
        
        // Load cart from database for logged-in users
        add_action('wp_login', array($this, 'load_cart_from_database'), 10, 2);
        add_action('wp_logout', array($this, 'save_cart_to_database'));
    }
    
    /**
     * Get cart contents
     */
    public function get_cart() {
        if (!isset($_SESSION[$this->cart_session_key])) {
            $_SESSION[$this->cart_session_key] = array();
        }
        
        return $_SESSION[$this->cart_session_key];
    }
    
    /**
     * Add item to cart
     */
    public function add_to_cart($product_id, $quantity = 1) {
        $product = SmartShop_Product::get_product($product_id);
        
        if (!$product || $product->status !== 'publish') {
            return false;
        }
        
        // Check stock
        if ($product->manage_stock && $product->stock_quantity !== null) {
            $current_quantity = $this->get_item_quantity($product_id);
            if (($current_quantity + $quantity) > $product->stock_quantity) {
                return false;
            }
        }
        
        $cart = $this->get_cart();
        $cart_item_key = $this->generate_cart_item_key($product_id);
        
        if (isset($cart[$cart_item_key])) {
            $cart[$cart_item_key]['quantity'] += $quantity;
        } else {
            $cart[$cart_item_key] = array(
                'product_id' => $product_id,
                'quantity' => $quantity,
                'added_at' => current_time('timestamp')
            );
        }
        
        $_SESSION[$this->cart_session_key] = $cart;
        
        // Save to database for logged-in users
        if (is_user_logged_in()) {
            $this->save_cart_to_database();
        }
        
        return true;
    }
    
    /**
     * Update cart item quantity
     */
    public function update_cart_item($product_id, $quantity) {
        $cart = $this->get_cart();
        $cart_item_key = $this->generate_cart_item_key($product_id);
        
        if (!isset($cart[$cart_item_key])) {
            return false;
        }
        
        if ($quantity <= 0) {
            return $this->remove_from_cart($product_id);
        }
        
        // Check stock
        $product = SmartShop_Product::get_product($product_id);
        if ($product && $product->manage_stock && $product->stock_quantity !== null) {
            if ($quantity > $product->stock_quantity) {
                return false;
            }
        }
        
        $cart[$cart_item_key]['quantity'] = $quantity;
        $_SESSION[$this->cart_session_key] = $cart;
        
        // Save to database for logged-in users
        if (is_user_logged_in()) {
            $this->save_cart_to_database();
        }
        
        return true;
    }
    
    /**
     * Remove item from cart
     */
    public function remove_from_cart($product_id) {
        $cart = $this->get_cart();
        $cart_item_key = $this->generate_cart_item_key($product_id);
        
        if (isset($cart[$cart_item_key])) {
            unset($cart[$cart_item_key]);
            $_SESSION[$this->cart_session_key] = $cart;
            
            // Save to database for logged-in users
            if (is_user_logged_in()) {
                $this->save_cart_to_database();
            }
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Clear cart
     */
    public function clear_cart() {
        $_SESSION[$this->cart_session_key] = array();
        
        // Clear from database for logged-in users
        if (is_user_logged_in()) {
            $this->clear_cart_from_database();
        }
    }
    
    /**
     * Get cart item quantity
     */
    public function get_item_quantity($product_id) {
        $cart = $this->get_cart();
        $cart_item_key = $this->generate_cart_item_key($product_id);
        
        return isset($cart[$cart_item_key]) ? $cart[$cart_item_key]['quantity'] : 0;
    }
    
    /**
     * Get cart count
     */
    public function get_cart_count() {
        $cart = $this->get_cart();
        $count = 0;
        
        foreach ($cart as $item) {
            $count += $item['quantity'];
        }
        
        return $count;
    }
    
    /**
     * Get cart total
     */
    public function get_cart_total() {
        $cart = $this->get_cart();
        $total = 0;
        
        foreach ($cart as $item) {
            $product = SmartShop_Product::get_product($item['product_id']);
            if ($product) {
                $price = $product->sale_price !== null ? $product->sale_price : $product->price;
                $total += $price * $item['quantity'];
            }
        }
        
        return $total;
    }
    
    /**
     * Get cart items with product details
     */
    public function get_cart_items() {
        $cart = $this->get_cart();
        $items = array();
        
        foreach ($cart as $cart_item_key => $item) {
            $product = SmartShop_Product::get_product($item['product_id']);
            if ($product) {
                $price = $product->sale_price !== null ? $product->sale_price : $product->price;
                $items[] = array(
                    'cart_item_key' => $cart_item_key,
                    'product_id' => $item['product_id'],
                    'product' => $product,
                    'quantity' => $item['quantity'],
                    'price' => $price,
                    'total' => $price * $item['quantity'],
                    'added_at' => $item['added_at']
                );
            }
        }
        
        return $items;
    }
    
    /**
     * Check if cart is empty
     */
    public function is_empty() {
        $cart = $this->get_cart();
        return empty($cart);
    }
    
    /**
     * Generate cart item key
     */
    private function generate_cart_item_key($product_id) {
        return md5($product_id);
    }
    
    /**
     * Save cart to database for logged-in users
     */
    public function save_cart_to_database() {
        if (!is_user_logged_in()) {
            return;
        }
        
        global $wpdb;
        $cart_table = SmartShop_Database::get_table_name('cart');
        $user_id = get_current_user_id();
        $session_id = session_id();
        
        // Clear existing cart items for this user
        $wpdb->delete(
            $cart_table,
            array('user_id' => $user_id),
            array('%d')
        );
        
        // Save current cart items
        $cart = $this->get_cart();
        foreach ($cart as $item) {
            $wpdb->insert(
                $cart_table,
                array(
                    'session_id' => $session_id,
                    'user_id' => $user_id,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity']
                ),
                array('%s', '%d', '%d', '%d')
            );
        }
    }
    
    /**
     * Load cart from database for logged-in users
     */
    public function load_cart_from_database($user_login = null, $user = null) {
        if (!is_user_logged_in()) {
            return;
        }
        
        global $wpdb;
        $cart_table = SmartShop_Database::get_table_name('cart');
        $user_id = get_current_user_id();
        
        $cart_items = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $cart_table WHERE user_id = %d",
            $user_id
        ));
        
        if ($cart_items) {
            $cart = array();
            foreach ($cart_items as $item) {
                $cart_item_key = $this->generate_cart_item_key($item->product_id);
                $cart[$cart_item_key] = array(
                    'product_id' => $item->product_id,
                    'quantity' => $item->quantity,
                    'added_at' => strtotime($item->created_at)
                );
            }
            $_SESSION[$this->cart_session_key] = $cart;
        }
    }
    
    /**
     * Clear cart from database
     */
    public function clear_cart_from_database() {
        if (!is_user_logged_in()) {
            return;
        }
        
        global $wpdb;
        $cart_table = SmartShop_Database::get_table_name('cart');
        $user_id = get_current_user_id();
        
        $wpdb->delete(
            $cart_table,
            array('user_id' => $user_id),
            array('%d')
        );
    }
    
    /**
     * AJAX handler for adding to cart
     */
    public function ajax_add_to_cart() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        $product_id = intval($_POST['product_id']);
        $quantity = intval($_POST['quantity'] ?? 1);
        
        if ($this->add_to_cart($product_id, $quantity)) {
            wp_send_json_success(array(
                'message' => __('Product added to cart successfully!', 'smartshop'),
                'cart_count' => $this->get_cart_count(),
                'cart_total' => SmartShop_Settings::format_currency($this->get_cart_total())
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to add product to cart. Please check stock availability.', 'smartshop')
            ));
        }
    }
    
    /**
     * AJAX handler for updating cart
     */
    public function ajax_update_cart() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        $product_id = intval($_POST['product_id']);
        $quantity = intval($_POST['quantity']);
        
        if ($this->update_cart_item($product_id, $quantity)) {
            wp_send_json_success(array(
                'message' => __('Cart updated successfully!', 'smartshop'),
                'cart_count' => $this->get_cart_count(),
                'cart_total' => SmartShop_Settings::format_currency($this->get_cart_total()),
                'cart_items' => $this->get_cart_items()
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to update cart. Please check stock availability.', 'smartshop')
            ));
        }
    }
    
    /**
     * AJAX handler for removing from cart
     */
    public function ajax_remove_from_cart() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        $product_id = intval($_POST['product_id']);
        
        if ($this->remove_from_cart($product_id)) {
            wp_send_json_success(array(
                'message' => __('Product removed from cart!', 'smartshop'),
                'cart_count' => $this->get_cart_count(),
                'cart_total' => SmartShop_Settings::format_currency($this->get_cart_total()),
                'cart_items' => $this->get_cart_items()
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to remove product from cart.', 'smartshop')
            ));
        }
    }
    
    /**
     * AJAX handler for getting cart
     */
    public function ajax_get_cart() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        wp_send_json_success(array(
            'cart_count' => $this->get_cart_count(),
            'cart_total' => SmartShop_Settings::format_currency($this->get_cart_total()),
            'cart_items' => $this->get_cart_items(),
            'is_empty' => $this->is_empty()
        ));
    }
    
    /**
     * AJAX handler for clearing cart
     */
    public function ajax_clear_cart() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        $this->clear_cart();
        
        wp_send_json_success(array(
            'message' => __('Cart cleared successfully!', 'smartshop'),
            'cart_count' => 0,
            'cart_total' => SmartShop_Settings::format_currency(0)
        ));
    }
}
