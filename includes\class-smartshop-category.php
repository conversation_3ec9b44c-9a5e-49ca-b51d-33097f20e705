<?php
/**
 * SmartShop Category Class
 * 
 * Handles category operations
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Category {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Constructor logic
    }
    
    /**
     * Create a new category
     */
    public static function create_category($data) {
        global $wpdb;
        
        $categories_table = SmartShop_Database::get_table_name('categories');
        
        $defaults = array(
            'name' => '',
            'slug' => '',
            'description' => '',
            'parent_id' => 0,
            'image_url' => '',
            'sort_order' => 0,
            'status' => 'active'
        );
        
        $data = wp_parse_args($data, $defaults);
        
        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = sanitize_title($data['name']);
        }
        
        // Check if slug already exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $categories_table WHERE slug = %s",
            $data['slug']
        ));
        
        if ($existing) {
            return false; // Slug already exists
        }
        
        $result = $wpdb->insert(
            $categories_table,
            array(
                'name' => sanitize_text_field($data['name']),
                'slug' => sanitize_title($data['slug']),
                'description' => sanitize_textarea_field($data['description']),
                'parent_id' => intval($data['parent_id']),
                'image_url' => esc_url_raw($data['image_url']),
                'sort_order' => intval($data['sort_order']),
                'status' => sanitize_text_field($data['status'])
            ),
            array('%s', '%s', '%s', '%d', '%s', '%d', '%s')
        );
        
        if ($result !== false) {
            return $wpdb->insert_id;
        }
        
        return false;
    }
    
    /**
     * Update category
     */
    public static function update_category($category_id, $data) {
        global $wpdb;
        
        $categories_table = SmartShop_Database::get_table_name('categories');
        
        // Remove id from data if present
        unset($data['id']);
        
        // Sanitize data
        $update_data = array();
        $update_format = array();
        
        if (isset($data['name'])) {
            $update_data['name'] = sanitize_text_field($data['name']);
            $update_format[] = '%s';
        }
        
        if (isset($data['slug'])) {
            $update_data['slug'] = sanitize_title($data['slug']);
            $update_format[] = '%s';
        }
        
        if (isset($data['description'])) {
            $update_data['description'] = sanitize_textarea_field($data['description']);
            $update_format[] = '%s';
        }
        
        if (isset($data['parent_id'])) {
            $update_data['parent_id'] = intval($data['parent_id']);
            $update_format[] = '%d';
        }
        
        if (isset($data['image_url'])) {
            $update_data['image_url'] = esc_url_raw($data['image_url']);
            $update_format[] = '%s';
        }
        
        if (isset($data['sort_order'])) {
            $update_data['sort_order'] = intval($data['sort_order']);
            $update_format[] = '%d';
        }
        
        if (isset($data['status'])) {
            $update_data['status'] = sanitize_text_field($data['status']);
            $update_format[] = '%s';
        }
        
        if (empty($update_data)) {
            return false;
        }
        
        $result = $wpdb->update(
            $categories_table,
            $update_data,
            array('id' => $category_id),
            $update_format,
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Delete category
     */
    public static function delete_category($category_id) {
        global $wpdb;
        
        $categories_table = SmartShop_Database::get_table_name('categories');
        
        // Check if category has products
        $products_table = SmartShop_Database::get_table_name('products');
        $product_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $products_table WHERE category_id = %d",
            $category_id
        ));
        
        if ($product_count > 0) {
            return false; // Cannot delete category with products
        }
        
        // Check if category has subcategories
        $subcategory_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $categories_table WHERE parent_id = %d",
            $category_id
        ));
        
        if ($subcategory_count > 0) {
            return false; // Cannot delete category with subcategories
        }
        
        $result = $wpdb->delete(
            $categories_table,
            array('id' => $category_id),
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Get category by ID
     */
    public static function get_category($category_id) {
        global $wpdb;
        
        $categories_table = SmartShop_Database::get_table_name('categories');
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $categories_table WHERE id = %d",
            $category_id
        ));
    }
    
    /**
     * Get category by slug
     */
    public static function get_category_by_slug($slug) {
        global $wpdb;
        
        $categories_table = SmartShop_Database::get_table_name('categories');
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $categories_table WHERE slug = %s",
            $slug
        ));
    }
    
    /**
     * Get all categories
     */
    public static function get_categories($args = array()) {
        global $wpdb;
        
        $categories_table = SmartShop_Database::get_table_name('categories');
        
        $defaults = array(
            'status' => 'active',
            'parent_id' => null,
            'orderby' => 'sort_order',
            'order' => 'ASC',
            'limit' => -1,
            'offset' => 0
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $where_conditions = array();
        $where_values = array();
        
        if ($args['status']) {
            $where_conditions[] = "status = %s";
            $where_values[] = $args['status'];
        }
        
        if ($args['parent_id'] !== null) {
            $where_conditions[] = "parent_id = %d";
            $where_values[] = $args['parent_id'];
        }
        
        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
        
        $order_clause = sprintf('ORDER BY %s %s', 
            sanitize_sql_orderby($args['orderby']), 
            $args['order'] === 'DESC' ? 'DESC' : 'ASC'
        );
        
        $limit_clause = '';
        if ($args['limit'] > 0) {
            $limit_clause = sprintf('LIMIT %d', $args['limit']);
            if ($args['offset'] > 0) {
                $limit_clause .= sprintf(' OFFSET %d', $args['offset']);
            }
        }
        
        $sql = "SELECT * FROM $categories_table $where_clause $order_clause $limit_clause";
        
        if (!empty($where_values)) {
            return $wpdb->get_results($wpdb->prepare($sql, $where_values));
        } else {
            return $wpdb->get_results($sql);
        }
    }
    
    /**
     * Get category tree (hierarchical)
     */
    public static function get_category_tree($parent_id = 0) {
        $categories = self::get_categories(array('parent_id' => $parent_id));
        
        foreach ($categories as $category) {
            $category->children = self::get_category_tree($category->id);
        }
        
        return $categories;
    }
    
    /**
     * Get category product count
     */
    public static function get_category_product_count($category_id) {
        global $wpdb;
        
        $products_table = SmartShop_Database::get_table_name('products');
        
        return $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $products_table WHERE category_id = %d AND status = 'publish'",
            $category_id
        ));
    }
    
    /**
     * Get category breadcrumbs
     */
    public static function get_category_breadcrumbs($category_id) {
        $breadcrumbs = array();
        $category = self::get_category($category_id);
        
        while ($category) {
            array_unshift($breadcrumbs, $category);
            
            if ($category->parent_id > 0) {
                $category = self::get_category($category->parent_id);
            } else {
                break;
            }
        }
        
        return $breadcrumbs;
    }
}
