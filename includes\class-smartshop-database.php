<?php
/**
 * SmartShop Database Class
 * 
 * Handles database operations and table creation
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Database {

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Database version
     */
    const DB_VERSION = '1.0.0';
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('init', array($this, 'check_database_version'));
    }

    /**
     * Check database version and update if needed
     */
    public function check_database_version() {
        $current_version = get_option('smartshop_db_version', '0.0.0');

        if (version_compare($current_version, self::DB_VERSION, '<')) {
            self::create_tables();
        }
    }
    
    /**
     * Create all plugin tables
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Products table
        $products_table = $wpdb->prefix . 'smartshop_products';
        $products_sql = "CREATE TABLE $products_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            slug varchar(255) NOT NULL,
            description longtext,
            short_description text,
            price decimal(10,2) NOT NULL DEFAULT '0.00',
            sale_price decimal(10,2) DEFAULT NULL,
            sku varchar(100) DEFAULT '',
            stock_quantity int(11) DEFAULT NULL,
            stock_status varchar(20) DEFAULT 'instock',
            manage_stock tinyint(1) DEFAULT 0,
            category_id bigint(20) unsigned DEFAULT NULL,
            image_url varchar(500) DEFAULT '',
            gallery text,
            weight varchar(50) DEFAULT '',
            dimensions varchar(100) DEFAULT '',
            status varchar(20) DEFAULT 'publish',
            featured tinyint(1) DEFAULT 0,
            meta_data longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY slug (slug),
            KEY category_id (category_id),
            KEY status (status),
            KEY featured (featured)
        ) $charset_collate;";
        
        // Categories table
        $categories_table = $wpdb->prefix . 'smartshop_categories';
        $categories_sql = "CREATE TABLE $categories_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            slug varchar(255) NOT NULL,
            description text,
            parent_id bigint(20) unsigned DEFAULT 0,
            image_url varchar(500) DEFAULT '',
            sort_order int(11) DEFAULT 0,
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY slug (slug),
            KEY parent_id (parent_id),
            KEY status (status)
        ) $charset_collate;";
        
        // Orders table
        $orders_table = $wpdb->prefix . 'smartshop_orders';
        $orders_sql = "CREATE TABLE $orders_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            order_number varchar(50) NOT NULL,
            user_id bigint(20) unsigned DEFAULT 0,
            status varchar(20) DEFAULT 'pending',
            total decimal(10,2) NOT NULL DEFAULT '0.00',
            subtotal decimal(10,2) NOT NULL DEFAULT '0.00',
            tax_total decimal(10,2) NOT NULL DEFAULT '0.00',
            shipping_total decimal(10,2) NOT NULL DEFAULT '0.00',
            discount_total decimal(10,2) NOT NULL DEFAULT '0.00',
            payment_method varchar(50) DEFAULT '',
            payment_status varchar(20) DEFAULT 'pending',
            currency varchar(10) DEFAULT 'USD',
            billing_data longtext,
            shipping_data longtext,
            order_notes text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY order_number (order_number),
            KEY user_id (user_id),
            KEY status (status),
            KEY payment_status (payment_status)
        ) $charset_collate;";
        
        // Order items table
        $order_items_table = $wpdb->prefix . 'smartshop_order_items';
        $order_items_sql = "CREATE TABLE $order_items_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            order_id bigint(20) unsigned NOT NULL,
            product_id bigint(20) unsigned NOT NULL,
            product_name varchar(255) NOT NULL,
            product_sku varchar(100) DEFAULT '',
            quantity int(11) NOT NULL DEFAULT 1,
            price decimal(10,2) NOT NULL DEFAULT '0.00',
            total decimal(10,2) NOT NULL DEFAULT '0.00',
            meta_data longtext,
            PRIMARY KEY (id),
            KEY order_id (order_id),
            KEY product_id (product_id)
        ) $charset_collate;";
        
        // Cart table (for persistent cart)
        $cart_table = $wpdb->prefix . 'smartshop_cart';
        $cart_sql = "CREATE TABLE $cart_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            session_id varchar(255) NOT NULL,
            user_id bigint(20) unsigned DEFAULT 0,
            product_id bigint(20) unsigned NOT NULL,
            quantity int(11) NOT NULL DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY session_id (session_id),
            KEY user_id (user_id),
            KEY product_id (product_id)
        ) $charset_collate;";
        
        // Settings table
        $settings_table = $wpdb->prefix . 'smartshop_settings';
        $settings_sql = "CREATE TABLE $settings_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            option_name varchar(255) NOT NULL,
            option_value longtext,
            autoload varchar(20) DEFAULT 'yes',
            PRIMARY KEY (id),
            UNIQUE KEY option_name (option_name)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        dbDelta($products_sql);
        dbDelta($categories_sql);
        dbDelta($orders_sql);
        dbDelta($order_items_sql);
        dbDelta($cart_sql);
        dbDelta($settings_sql);
        
        // Insert default categories
        self::insert_default_categories();
        
        // Update database version
        update_option('smartshop_db_version', self::DB_VERSION);
    }
    
    /**
     * Insert default categories
     */
    private static function insert_default_categories() {
        global $wpdb;
        
        $categories_table = $wpdb->prefix . 'smartshop_categories';
        
        $default_categories = array(
            array('name' => 'Electronics', 'slug' => 'electronics', 'description' => 'Electronic devices and gadgets'),
            array('name' => 'Fashion', 'slug' => 'fashion', 'description' => 'Clothing and accessories'),
            array('name' => 'Grocery', 'slug' => 'grocery', 'description' => 'Food and beverages'),
            array('name' => 'Beauty', 'slug' => 'beauty', 'description' => 'Beauty and personal care products'),
            array('name' => 'Home & Garden', 'slug' => 'home-garden', 'description' => 'Home and garden items'),
        );
        
        foreach ($default_categories as $category) {
            $exists = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $categories_table WHERE slug = %s",
                $category['slug']
            ));
            
            if (!$exists) {
                $wpdb->insert(
                    $categories_table,
                    $category,
                    array('%s', '%s', '%s')
                );
            }
        }
    }
    
    /**
     * Drop all plugin tables
     */
    public static function drop_tables() {
        global $wpdb;
        
        $tables = array(
            $wpdb->prefix . 'smartshop_products',
            $wpdb->prefix . 'smartshop_categories',
            $wpdb->prefix . 'smartshop_orders',
            $wpdb->prefix . 'smartshop_order_items',
            $wpdb->prefix . 'smartshop_cart',
            $wpdb->prefix . 'smartshop_settings'
        );
        
        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS $table");
        }
        
        // Remove database version option
        delete_option('smartshop_db_version');
    }
    
    /**
     * Get table name with prefix
     */
    public static function get_table_name($table) {
        global $wpdb;
        return $wpdb->prefix . 'smartshop_' . $table;
    }

    /**
     * Check if all tables exist
     */
    public static function tables_exist() {
        global $wpdb;

        $tables = array('products', 'categories', 'orders', 'order_items', 'cart', 'settings');

        foreach ($tables as $table) {
            $table_name = self::get_table_name($table);
            $exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");

            if ($exists !== $table_name) {
                return false;
            }
        }

        return true;
    }
}
