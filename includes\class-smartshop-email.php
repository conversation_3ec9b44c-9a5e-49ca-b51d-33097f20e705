<?php
/**
 * SmartShop Email Class
 * 
 * Handles email notifications
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Email {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Hook into order creation
        add_action('smartshop_order_created', array($this, 'send_order_confirmation'), 10, 1);
        add_action('smartshop_order_status_changed', array($this, 'send_order_status_update'), 10, 2);
    }
    
    /**
     * Send order confirmation email
     */
    public function send_order_confirmation($order_id) {
        if (!SmartShop_Settings::get_option('enable_order_emails', 1)) {
            return;
        }
        
        $order = SmartShop_Order::get_order($order_id);
        if (!$order) {
            return;
        }
        
        $billing_data = unserialize($order->billing_data);
        $to = $billing_data['email'];
        $subject = sprintf(__('Order Confirmation - %s', 'smartshop'), $order->order_number);
        
        $message = $this->get_order_confirmation_template($order);
        
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . SmartShop_Settings::get_option('from_name', get_bloginfo('name')) . ' <' . SmartShop_Settings::get_option('from_email', get_option('admin_email')) . '>'
        );
        
        wp_mail($to, $subject, $message, $headers);
        
        // Send copy to admin
        $admin_email = SmartShop_Settings::get_option('admin_email', get_option('admin_email'));
        if ($admin_email && $admin_email !== $to) {
            $admin_subject = sprintf(__('New Order Received - %s', 'smartshop'), $order->order_number);
            wp_mail($admin_email, $admin_subject, $message, $headers);
        }
    }
    
    /**
     * Send order status update email
     */
    public function send_order_status_update($order_id, $new_status) {
        if (!SmartShop_Settings::get_option('enable_order_emails', 1)) {
            return;
        }
        
        $order = SmartShop_Order::get_order($order_id);
        if (!$order) {
            return;
        }
        
        $billing_data = unserialize($order->billing_data);
        $to = $billing_data['email'];
        $subject = sprintf(__('Order Status Update - %s', 'smartshop'), $order->order_number);
        
        $message = $this->get_order_status_update_template($order, $new_status);
        
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . SmartShop_Settings::get_option('from_name', get_bloginfo('name')) . ' <' . SmartShop_Settings::get_option('from_email', get_option('admin_email')) . '>'
        );
        
        wp_mail($to, $subject, $message, $headers);
    }
    
    /**
     * Get order confirmation email template
     */
    private function get_order_confirmation_template($order) {
        $billing_data = unserialize($order->billing_data);
        $order_items = SmartShop_Order::get_order_items($order->id);
        
        $message = '<html><body>';
        $message .= '<div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">';
        
        // Header
        $message .= '<div style="background-color: #f8f9fa; padding: 20px; text-align: center;">';
        $message .= '<h1 style="color: #333; margin: 0;">' . get_bloginfo('name') . '</h1>';
        $message .= '<h2 style="color: #666; margin: 10px 0 0 0;">' . __('Order Confirmation', 'smartshop') . '</h2>';
        $message .= '</div>';
        
        // Order details
        $message .= '<div style="padding: 20px;">';
        $message .= '<h3>' . __('Order Details', 'smartshop') . '</h3>';
        $message .= '<p><strong>' . __('Order Number:', 'smartshop') . '</strong> ' . $order->order_number . '</p>';
        $message .= '<p><strong>' . __('Order Date:', 'smartshop') . '</strong> ' . date_i18n(get_option('date_format'), strtotime($order->created_at)) . '</p>';
        $message .= '<p><strong>' . __('Payment Method:', 'smartshop') . '</strong> ' . $this->get_payment_method_name($order->payment_method) . '</p>';
        
        // Billing details
        $message .= '<h3>' . __('Billing Details', 'smartshop') . '</h3>';
        $message .= '<p>';
        $message .= $billing_data['first_name'] . ' ' . $billing_data['last_name'] . '<br>';
        $message .= $billing_data['email'] . '<br>';
        $message .= $billing_data['phone'] . '<br>';
        $message .= $billing_data['address'] . '<br>';
        $message .= $billing_data['city'];
        if (!empty($billing_data['state'])) {
            $message .= ', ' . $billing_data['state'];
        }
        if (!empty($billing_data['postcode'])) {
            $message .= ' ' . $billing_data['postcode'];
        }
        if (!empty($billing_data['country'])) {
            $message .= '<br>' . $billing_data['country'];
        }
        $message .= '</p>';
        
        // Order items
        $message .= '<h3>' . __('Order Items', 'smartshop') . '</h3>';
        $message .= '<table style="width: 100%; border-collapse: collapse;">';
        $message .= '<thead>';
        $message .= '<tr style="background-color: #f8f9fa;">';
        $message .= '<th style="padding: 10px; text-align: left; border: 1px solid #ddd;">' . __('Product', 'smartshop') . '</th>';
        $message .= '<th style="padding: 10px; text-align: center; border: 1px solid #ddd;">' . __('Quantity', 'smartshop') . '</th>';
        $message .= '<th style="padding: 10px; text-align: right; border: 1px solid #ddd;">' . __('Price', 'smartshop') . '</th>';
        $message .= '<th style="padding: 10px; text-align: right; border: 1px solid #ddd;">' . __('Total', 'smartshop') . '</th>';
        $message .= '</tr>';
        $message .= '</thead>';
        $message .= '<tbody>';
        
        foreach ($order_items as $item) {
            $message .= '<tr>';
            $message .= '<td style="padding: 10px; border: 1px solid #ddd;">' . $item->product_name;
            if (!empty($item->product_sku)) {
                $message .= ' <small>(SKU: ' . $item->product_sku . ')</small>';
            }
            $message .= '</td>';
            $message .= '<td style="padding: 10px; text-align: center; border: 1px solid #ddd;">' . $item->quantity . '</td>';
            $message .= '<td style="padding: 10px; text-align: right; border: 1px solid #ddd;">' . SmartShop_Settings::format_currency($item->price) . '</td>';
            $message .= '<td style="padding: 10px; text-align: right; border: 1px solid #ddd;">' . SmartShop_Settings::format_currency($item->total) . '</td>';
            $message .= '</tr>';
        }
        
        $message .= '</tbody>';
        $message .= '</table>';
        
        // Order totals
        $message .= '<div style="margin-top: 20px; text-align: right;">';
        $message .= '<p><strong>' . __('Subtotal:', 'smartshop') . '</strong> ' . SmartShop_Settings::format_currency($order->subtotal) . '</p>';
        
        if ($order->tax_total > 0) {
            $message .= '<p><strong>' . __('Tax:', 'smartshop') . '</strong> ' . SmartShop_Settings::format_currency($order->tax_total) . '</p>';
        }
        
        if ($order->shipping_total > 0) {
            $message .= '<p><strong>' . __('Shipping:', 'smartshop') . '</strong> ' . SmartShop_Settings::format_currency($order->shipping_total) . '</p>';
        }
        
        if ($order->discount_total > 0) {
            $message .= '<p><strong>' . __('Discount:', 'smartshop') . '</strong> -' . SmartShop_Settings::format_currency($order->discount_total) . '</p>';
        }
        
        $message .= '<p style="font-size: 18px;"><strong>' . __('Total:', 'smartshop') . '</strong> ' . SmartShop_Settings::format_currency($order->total) . '</p>';
        $message .= '</div>';
        
        // Payment instructions
        if ($order->payment_method === 'cod') {
            $message .= '<div style="background-color: #fff3cd; padding: 15px; margin-top: 20px; border-radius: 5px;">';
            $message .= '<h4>' . __('Payment Instructions', 'smartshop') . '</h4>';
            $message .= '<p>' . SmartShop_Settings::get_option('cod_instructions', __('Pay with cash upon delivery.', 'smartshop')) . '</p>';
            $message .= '</div>';
        } elseif ($order->payment_method === 'manual') {
            $message .= '<div style="background-color: #d1ecf1; padding: 15px; margin-top: 20px; border-radius: 5px;">';
            $message .= '<h4>' . __('Payment Instructions', 'smartshop') . '</h4>';
            $message .= '<p>' . SmartShop_Settings::get_option('manual_payment_instructions', __('Please transfer the amount to our bank account.', 'smartshop')) . '</p>';
            $message .= '</div>';
        }
        
        // Footer
        $message .= '<div style="background-color: #f8f9fa; padding: 20px; margin-top: 30px; text-align: center;">';
        $message .= '<p>' . __('Thank you for your order!', 'smartshop') . '</p>';
        $message .= '<p><small>' . sprintf(__('This email was sent from %s', 'smartshop'), get_bloginfo('name')) . '</small></p>';
        $message .= '</div>';
        
        $message .= '</div>';
        $message .= '</div>';
        $message .= '</body></html>';
        
        return $message;
    }
    
    /**
     * Get order status update email template
     */
    private function get_order_status_update_template($order, $new_status) {
        $billing_data = unserialize($order->billing_data);
        
        $message = '<html><body>';
        $message .= '<div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">';
        
        // Header
        $message .= '<div style="background-color: #f8f9fa; padding: 20px; text-align: center;">';
        $message .= '<h1 style="color: #333; margin: 0;">' . get_bloginfo('name') . '</h1>';
        $message .= '<h2 style="color: #666; margin: 10px 0 0 0;">' . __('Order Status Update', 'smartshop') . '</h2>';
        $message .= '</div>';
        
        // Status update
        $message .= '<div style="padding: 20px;">';
        $message .= '<p>' . sprintf(__('Hello %s,', 'smartshop'), $billing_data['first_name']) . '</p>';
        $message .= '<p>' . sprintf(__('Your order %s status has been updated to: %s', 'smartshop'), $order->order_number, '<strong>' . $this->get_status_name($new_status) . '</strong>') . '</p>';
        
        // Order details
        $message .= '<h3>' . __('Order Details', 'smartshop') . '</h3>';
        $message .= '<p><strong>' . __('Order Number:', 'smartshop') . '</strong> ' . $order->order_number . '</p>';
        $message .= '<p><strong>' . __('Order Date:', 'smartshop') . '</strong> ' . date_i18n(get_option('date_format'), strtotime($order->created_at)) . '</p>';
        $message .= '<p><strong>' . __('Total:', 'smartshop') . '</strong> ' . SmartShop_Settings::format_currency($order->total) . '</p>';
        
        // Footer
        $message .= '<div style="background-color: #f8f9fa; padding: 20px; margin-top: 30px; text-align: center;">';
        $message .= '<p>' . __('Thank you for your business!', 'smartshop') . '</p>';
        $message .= '<p><small>' . sprintf(__('This email was sent from %s', 'smartshop'), get_bloginfo('name')) . '</small></p>';
        $message .= '</div>';
        
        $message .= '</div>';
        $message .= '</div>';
        $message .= '</body></html>';
        
        return $message;
    }
    
    /**
     * Get payment method name
     */
    private function get_payment_method_name($method) {
        $methods = array(
            'cod' => __('Cash on Delivery', 'smartshop'),
            'manual' => __('Manual Payment', 'smartshop'),
            'stripe' => __('Credit Card (Stripe)', 'smartshop'),
            'paypal' => __('PayPal', 'smartshop')
        );
        
        return isset($methods[$method]) ? $methods[$method] : $method;
    }
    
    /**
     * Get status name
     */
    private function get_status_name($status) {
        $statuses = array(
            'pending' => __('Pending', 'smartshop'),
            'processing' => __('Processing', 'smartshop'),
            'shipped' => __('Shipped', 'smartshop'),
            'delivered' => __('Delivered', 'smartshop'),
            'completed' => __('Completed', 'smartshop'),
            'cancelled' => __('Cancelled', 'smartshop'),
            'refunded' => __('Refunded', 'smartshop')
        );
        
        return isset($statuses[$status]) ? $statuses[$status] : $status;
    }
}
