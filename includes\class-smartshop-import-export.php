<?php
/**
 * SmartShop Import/Export Class
 * 
 * Handles CSV import and export functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Import_Export {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('wp_ajax_smartshop_export_products', array($this, 'export_products'));
        add_action('wp_ajax_smartshop_export_orders', array($this, 'export_orders'));
        add_action('wp_ajax_smartshop_import_products', array($this, 'import_products'));
    }
    
    /**
     * Export products to CSV
     */
    public function export_products() {
        check_ajax_referer('smartshop_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'smartshop'));
        }
        
        $products = SmartShop_Product::get_products(array(
            'limit' => -1,
            'status' => 'all'
        ));
        
        $filename = 'smartshop-products-' . date('Y-m-d-H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        $headers = array(
            'ID',
            'Name',
            'Slug',
            'Description',
            'Short Description',
            'SKU',
            'Price',
            'Sale Price',
            'Category ID',
            'Image URL',
            'Gallery Images',
            'Stock Status',
            'Stock Quantity',
            'Manage Stock',
            'Weight',
            'Dimensions',
            'Featured',
            'Status',
            'Created At'
        );
        
        fputcsv($output, $headers);
        
        // Product data
        foreach ($products as $product) {
            $row = array(
                $product->id,
                $product->name,
                $product->slug,
                $product->description,
                $product->short_description,
                $product->sku,
                $product->price,
                $product->sale_price,
                $product->category_id,
                $product->image_url,
                $product->gallery_images,
                $product->stock_status,
                $product->stock_quantity,
                $product->manage_stock ? 'yes' : 'no',
                $product->weight,
                $product->dimensions,
                $product->featured ? 'yes' : 'no',
                $product->status,
                $product->created_at
            );
            
            fputcsv($output, $row);
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Export orders to CSV
     */
    public function export_orders() {
        check_ajax_referer('smartshop_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'smartshop'));
        }
        
        $orders = SmartShop_Order::get_orders(array(
            'limit' => -1
        ));
        
        $filename = 'smartshop-orders-' . date('Y-m-d-H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        $headers = array(
            'Order ID',
            'Order Number',
            'Customer Name',
            'Customer Email',
            'Customer Phone',
            'Billing Address',
            'Subtotal',
            'Tax Total',
            'Shipping Total',
            'Discount Total',
            'Total',
            'Payment Method',
            'Payment Status',
            'Order Status',
            'Order Notes',
            'Created At'
        );
        
        fputcsv($output, $headers);
        
        // Order data
        foreach ($orders as $order) {
            $billing_data = unserialize($order->billing_data);
            
            $billing_address = implode(', ', array_filter(array(
                $billing_data['address'],
                $billing_data['city'],
                $billing_data['state'],
                $billing_data['postcode'],
                $billing_data['country']
            )));
            
            $row = array(
                $order->id,
                $order->order_number,
                $billing_data['first_name'] . ' ' . $billing_data['last_name'],
                $billing_data['email'],
                $billing_data['phone'],
                $billing_address,
                $order->subtotal,
                $order->tax_total,
                $order->shipping_total,
                $order->discount_total,
                $order->total,
                $order->payment_method,
                $order->payment_status,
                $order->status,
                $order->order_notes,
                $order->created_at
            );
            
            fputcsv($output, $row);
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Import products from CSV
     */
    public function import_products() {
        check_ajax_referer('smartshop_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have sufficient permissions to perform this action.', 'smartshop')
            ));
        }
        
        if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error(array(
                'message' => __('Please select a valid CSV file.', 'smartshop')
            ));
        }
        
        $file = $_FILES['csv_file'];
        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        
        if (strtolower($file_extension) !== 'csv') {
            wp_send_json_error(array(
                'message' => __('Please upload a CSV file.', 'smartshop')
            ));
        }
        
        $handle = fopen($file['tmp_name'], 'r');
        if (!$handle) {
            wp_send_json_error(array(
                'message' => __('Unable to read the CSV file.', 'smartshop')
            ));
        }
        
        $headers = fgetcsv($handle);
        $imported = 0;
        $errors = array();
        $row_number = 1;
        
        while (($data = fgetcsv($handle)) !== false) {
            $row_number++;
            
            if (count($data) !== count($headers)) {
                $errors[] = sprintf(__('Row %d: Column count mismatch.', 'smartshop'), $row_number);
                continue;
            }
            
            $product_data = array_combine($headers, $data);
            
            // Validate required fields
            if (empty($product_data['Name'])) {
                $errors[] = sprintf(__('Row %d: Product name is required.', 'smartshop'), $row_number);
                continue;
            }
            
            if (empty($product_data['Price']) || !is_numeric($product_data['Price'])) {
                $errors[] = sprintf(__('Row %d: Valid price is required.', 'smartshop'), $row_number);
                continue;
            }
            
            // Prepare product data for insertion
            $insert_data = array(
                'name' => sanitize_text_field($product_data['Name']),
                'slug' => !empty($product_data['Slug']) ? sanitize_title($product_data['Slug']) : sanitize_title($product_data['Name']),
                'description' => wp_kses_post($product_data['Description'] ?? ''),
                'short_description' => sanitize_textarea_field($product_data['Short Description'] ?? ''),
                'sku' => sanitize_text_field($product_data['SKU'] ?? ''),
                'price' => floatval($product_data['Price']),
                'sale_price' => !empty($product_data['Sale Price']) ? floatval($product_data['Sale Price']) : null,
                'category_id' => !empty($product_data['Category ID']) ? intval($product_data['Category ID']) : null,
                'image_url' => esc_url_raw($product_data['Image URL'] ?? ''),
                'gallery_images' => sanitize_text_field($product_data['Gallery Images'] ?? ''),
                'stock_status' => in_array($product_data['Stock Status'] ?? '', array('instock', 'outofstock')) ? $product_data['Stock Status'] : 'instock',
                'stock_quantity' => !empty($product_data['Stock Quantity']) ? intval($product_data['Stock Quantity']) : null,
                'manage_stock' => ($product_data['Manage Stock'] ?? '') === 'yes',
                'weight' => sanitize_text_field($product_data['Weight'] ?? ''),
                'dimensions' => sanitize_text_field($product_data['Dimensions'] ?? ''),
                'featured' => ($product_data['Featured'] ?? '') === 'yes',
                'status' => in_array($product_data['Status'] ?? '', array('publish', 'draft')) ? $product_data['Status'] : 'publish'
            );
            
            // Check if updating existing product
            if (!empty($product_data['ID']) && is_numeric($product_data['ID'])) {
                $product_id = intval($product_data['ID']);
                if (SmartShop_Product::update_product($product_id, $insert_data)) {
                    $imported++;
                } else {
                    $errors[] = sprintf(__('Row %d: Failed to update product ID %d.', 'smartshop'), $row_number, $product_id);
                }
            } else {
                // Create new product
                $product_id = SmartShop_Product::create_product($insert_data);
                if ($product_id) {
                    $imported++;
                } else {
                    $errors[] = sprintf(__('Row %d: Failed to create product.', 'smartshop'), $row_number);
                }
            }
        }
        
        fclose($handle);
        
        $message = sprintf(__('Import completed. %d products imported successfully.', 'smartshop'), $imported);
        
        if (!empty($errors)) {
            $message .= ' ' . sprintf(__('%d errors occurred:', 'smartshop'), count($errors));
            $message .= '<ul><li>' . implode('</li><li>', array_slice($errors, 0, 10)) . '</li></ul>';
            
            if (count($errors) > 10) {
                $message .= sprintf(__('... and %d more errors.', 'smartshop'), count($errors) - 10);
            }
        }
        
        wp_send_json_success(array(
            'message' => $message,
            'imported' => $imported,
            'errors' => count($errors)
        ));
    }
    
    /**
     * Get sample CSV data for products
     */
    public static function get_sample_product_csv() {
        $sample_data = array(
            array(
                'Name' => 'Sample Product 1',
                'Slug' => 'sample-product-1',
                'Description' => 'This is a sample product description.',
                'Short Description' => 'Sample product short description.',
                'SKU' => 'SP001',
                'Price' => '29.99',
                'Sale Price' => '24.99',
                'Category ID' => '1',
                'Image URL' => 'https://example.com/image1.jpg',
                'Gallery Images' => '',
                'Stock Status' => 'instock',
                'Stock Quantity' => '100',
                'Manage Stock' => 'yes',
                'Weight' => '1.5',
                'Dimensions' => '10x10x5',
                'Featured' => 'no',
                'Status' => 'publish'
            ),
            array(
                'Name' => 'Sample Product 2',
                'Slug' => 'sample-product-2',
                'Description' => 'Another sample product description.',
                'Short Description' => 'Another sample product short description.',
                'SKU' => 'SP002',
                'Price' => '49.99',
                'Sale Price' => '',
                'Category ID' => '2',
                'Image URL' => 'https://example.com/image2.jpg',
                'Gallery Images' => '',
                'Stock Status' => 'instock',
                'Stock Quantity' => '50',
                'Manage Stock' => 'yes',
                'Weight' => '2.0',
                'Dimensions' => '15x15x8',
                'Featured' => 'yes',
                'Status' => 'publish'
            )
        );
        
        return $sample_data;
    }
}
