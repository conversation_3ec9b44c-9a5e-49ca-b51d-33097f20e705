<?php
/**
 * SmartShop Order Class
 * 
 * Handles order operations and management
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Order {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('wp_ajax_smartshop_create_order', array($this, 'ajax_create_order'));
        add_action('wp_ajax_nopriv_smartshop_create_order', array($this, 'ajax_create_order'));
        add_action('wp_ajax_smartshop_update_order_status', array($this, 'ajax_update_order_status'));
    }
    
    /**
     * Create a new order
     */
    public static function create_order($order_data) {
        global $wpdb;
        
        $orders_table = SmartShop_Database::get_table_name('orders');
        $order_items_table = SmartShop_Database::get_table_name('order_items');
        
        // Generate order number
        $order_number = self::generate_order_number();
        
        // Prepare order data
        $order = array(
            'order_number' => $order_number,
            'user_id' => is_user_logged_in() ? get_current_user_id() : 0,
            'status' => 'pending',
            'total' => floatval($order_data['total']),
            'subtotal' => floatval($order_data['subtotal']),
            'tax_total' => floatval($order_data['tax_total'] ?? 0),
            'shipping_total' => floatval($order_data['shipping_total'] ?? 0),
            'discount_total' => floatval($order_data['discount_total'] ?? 0),
            'payment_method' => sanitize_text_field($order_data['payment_method']),
            'payment_status' => 'pending',
            'currency' => SmartShop_Settings::get_option('currency', 'USD'),
            'billing_data' => serialize($order_data['billing_data']),
            'shipping_data' => isset($order_data['shipping_data']) ? serialize($order_data['shipping_data']) : '',
            'order_notes' => sanitize_textarea_field($order_data['order_notes'] ?? '')
        );
        
        // Insert order
        $result = $wpdb->insert($orders_table, $order);
        
        if ($result === false) {
            return false;
        }
        
        $order_id = $wpdb->insert_id;
        
        // Insert order items
        if (isset($order_data['items']) && is_array($order_data['items'])) {
            foreach ($order_data['items'] as $item) {
                $order_item = array(
                    'order_id' => $order_id,
                    'product_id' => intval($item['product_id']),
                    'product_name' => sanitize_text_field($item['product_name']),
                    'product_sku' => sanitize_text_field($item['product_sku'] ?? ''),
                    'quantity' => intval($item['quantity']),
                    'price' => floatval($item['price']),
                    'total' => floatval($item['total']),
                    'meta_data' => isset($item['meta_data']) ? serialize($item['meta_data']) : ''
                );
                
                $wpdb->insert($order_items_table, $order_item);
                
                // Update product stock
                self::update_product_stock($item['product_id'], $item['quantity']);
            }
        }
        
        // Send order confirmation email
        self::send_order_confirmation_email($order_id);
        
        return $order_id;
    }
    
    /**
     * Update order status
     */
    public static function update_order_status($order_id, $status) {
        global $wpdb;
        
        $orders_table = SmartShop_Database::get_table_name('orders');
        
        $result = $wpdb->update(
            $orders_table,
            array('status' => sanitize_text_field($status)),
            array('id' => $order_id),
            array('%s'),
            array('%d')
        );
        
        if ($result !== false) {
            // Send status update email
            self::send_order_status_email($order_id, $status);
        }
        
        return $result !== false;
    }
    
    /**
     * Get order by ID
     */
    public static function get_order($order_id) {
        global $wpdb;
        
        $orders_table = SmartShop_Database::get_table_name('orders');
        $order_items_table = SmartShop_Database::get_table_name('order_items');
        
        $order = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $orders_table WHERE id = %d",
            $order_id
        ));
        
        if ($order) {
            $order->billing_data = unserialize($order->billing_data);
            $order->shipping_data = unserialize($order->shipping_data);
            
            // Get order items
            $order->items = $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM $order_items_table WHERE order_id = %d",
                $order_id
            ));
            
            foreach ($order->items as $item) {
                $item->meta_data = unserialize($item->meta_data);
            }
        }
        
        return $order;
    }
    
    /**
     * Get order by order number
     */
    public static function get_order_by_number($order_number) {
        global $wpdb;
        
        $orders_table = SmartShop_Database::get_table_name('orders');
        
        $order_id = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $orders_table WHERE order_number = %s",
            $order_number
        ));
        
        if ($order_id) {
            return self::get_order($order_id);
        }
        
        return null;
    }
    
    /**
     * Get orders with filters
     */
    public static function get_orders($args = array()) {
        global $wpdb;
        
        $defaults = array(
            'limit' => 20,
            'offset' => 0,
            'status' => null,
            'user_id' => null,
            'payment_status' => null,
            'orderby' => 'created_at',
            'order' => 'DESC',
            'date_from' => null,
            'date_to' => null
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $orders_table = SmartShop_Database::get_table_name('orders');
        
        $where_conditions = array();
        $where_values = array();
        
        if ($args['status']) {
            $where_conditions[] = "status = %s";
            $where_values[] = $args['status'];
        }
        
        if ($args['user_id']) {
            $where_conditions[] = "user_id = %d";
            $where_values[] = $args['user_id'];
        }
        
        if ($args['payment_status']) {
            $where_conditions[] = "payment_status = %s";
            $where_values[] = $args['payment_status'];
        }
        
        if ($args['date_from']) {
            $where_conditions[] = "created_at >= %s";
            $where_values[] = $args['date_from'];
        }
        
        if ($args['date_to']) {
            $where_conditions[] = "created_at <= %s";
            $where_values[] = $args['date_to'];
        }
        
        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
        
        $orderby = sanitize_sql_orderby($args['orderby']);
        $order = strtoupper($args['order']) === 'ASC' ? 'ASC' : 'DESC';
        
        $sql = "SELECT * FROM $orders_table $where_clause ORDER BY $orderby $order LIMIT %d OFFSET %d";
        $where_values[] = intval($args['limit']);
        $where_values[] = intval($args['offset']);
        
        $query = $wpdb->prepare($sql, $where_values);
        $orders = $wpdb->get_results($query);
        
        foreach ($orders as $order) {
            $order->billing_data = unserialize($order->billing_data);
            $order->shipping_data = unserialize($order->shipping_data);
        }
        
        return $orders;
    }
    
    /**
     * Get order count
     */
    public static function get_order_count($args = array()) {
        global $wpdb;
        
        $defaults = array(
            'status' => null,
            'user_id' => null,
            'payment_status' => null,
            'date_from' => null,
            'date_to' => null
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $orders_table = SmartShop_Database::get_table_name('orders');
        
        $where_conditions = array();
        $where_values = array();
        
        if ($args['status']) {
            $where_conditions[] = "status = %s";
            $where_values[] = $args['status'];
        }
        
        if ($args['user_id']) {
            $where_conditions[] = "user_id = %d";
            $where_values[] = $args['user_id'];
        }
        
        if ($args['payment_status']) {
            $where_conditions[] = "payment_status = %s";
            $where_values[] = $args['payment_status'];
        }
        
        if ($args['date_from']) {
            $where_conditions[] = "created_at >= %s";
            $where_values[] = $args['date_from'];
        }
        
        if ($args['date_to']) {
            $where_conditions[] = "created_at <= %s";
            $where_values[] = $args['date_to'];
        }
        
        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
        
        $sql = "SELECT COUNT(*) FROM $orders_table $where_clause";
        
        if (!empty($where_values)) {
            $query = $wpdb->prepare($sql, $where_values);
        } else {
            $query = $sql;
        }
        
        return intval($wpdb->get_var($query));
    }
    
    /**
     * Generate unique order number
     */
    private static function generate_order_number() {
        $prefix = SmartShop_Settings::get_option('order_number_prefix', 'SS');
        $suffix = SmartShop_Settings::get_option('order_number_suffix', '');
        $length = SmartShop_Settings::get_option('order_number_length', 6);
        
        do {
            $number = $prefix . str_pad(rand(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT) . $suffix;
        } while (self::order_number_exists($number));
        
        return $number;
    }
    
    /**
     * Check if order number exists
     */
    private static function order_number_exists($order_number) {
        global $wpdb;
        
        $orders_table = SmartShop_Database::get_table_name('orders');
        
        $exists = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $orders_table WHERE order_number = %s",
            $order_number
        ));
        
        return $exists !== null;
    }
    
    /**
     * Update product stock after order
     */
    private static function update_product_stock($product_id, $quantity) {
        $product = SmartShop_Product::get_product($product_id);
        
        if ($product && $product->manage_stock && $product->stock_quantity !== null) {
            $new_stock = $product->stock_quantity - $quantity;
            $stock_status = $new_stock > 0 ? 'instock' : 'outofstock';
            
            SmartShop_Product::update_product($product_id, array(
                'stock_quantity' => max(0, $new_stock),
                'stock_status' => $stock_status
            ));
        }
    }
    
    /**
     * Send order confirmation email
     */
    private static function send_order_confirmation_email($order_id) {
        if (!SmartShop_Settings::get_option('enable_order_emails', 1)) {
            return;
        }
        
        $order = self::get_order($order_id);
        if (!$order) {
            return;
        }
        
        $to = $order->billing_data['email'];
        $subject = sprintf(__('Order Confirmation - %s', 'smartshop'), $order->order_number);
        
        $message = self::get_order_email_template($order, 'confirmation');
        
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . SmartShop_Settings::get_option('from_name') . ' <' . SmartShop_Settings::get_option('from_email') . '>'
        );
        
        wp_mail($to, $subject, $message, $headers);
        
        // Send copy to admin
        $admin_email = SmartShop_Settings::get_option('admin_email');
        if ($admin_email && $admin_email !== $to) {
            wp_mail($admin_email, 'New Order: ' . $order->order_number, $message, $headers);
        }
    }
    
    /**
     * Update order status
     */
    public static function update_order_status($order_id, $status) {
        global $wpdb;

        $orders_table = SmartShop_Database::get_table_name('orders');

        $result = $wpdb->update(
            $orders_table,
            array('status' => $status),
            array('id' => $order_id),
            array('%s'),
            array('%d')
        );

        if ($result !== false) {
            // Send status update email
            self::send_order_status_email($order_id, $status);
            return true;
        }

        return false;
    }

    /**
     * Send order status update email
     */
    private static function send_order_status_email($order_id, $status) {
        if (!SmartShop_Settings::get_option('enable_order_emails', 1)) {
            return;
        }

        $order = self::get_order($order_id);
        if (!$order) {
            return;
        }

        $to = $order->billing_data['email'];
        $subject = sprintf(__('Order Status Update - %s', 'smartshop'), $order->order_number);

        $message = self::get_order_email_template($order, 'status_update');

        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . SmartShop_Settings::get_option('from_name') . ' <' . SmartShop_Settings::get_option('from_email') . '>'
        );

        wp_mail($to, $subject, $message, $headers);
    }
    
    /**
     * Get order email template
     */
    private static function get_order_email_template($order, $type = 'confirmation') {
        ob_start();
        include SMARTSHOP_PLUGIN_PATH . 'templates/emails/order-' . $type . '.php';
        return ob_get_clean();
    }
    
    /**
     * AJAX handler for creating order
     */
    public function ajax_create_order() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        $cart = SmartShop_Cart::get_instance();
        
        if ($cart->is_empty()) {
            wp_send_json_error(array(
                'message' => __('Your cart is empty.', 'smartshop')
            ));
        }
        
        // Validate required fields
        $required_fields = array('first_name', 'last_name', 'email', 'phone', 'address', 'city');
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                wp_send_json_error(array(
                    'message' => sprintf(__('%s is required.', 'smartshop'), ucfirst(str_replace('_', ' ', $field)))
                ));
            }
        }
        
        // Prepare order data
        $cart_items = $cart->get_cart_items();
        $subtotal = $cart->get_cart_total();
        $tax_total = 0; // Calculate tax if enabled
        $shipping_total = 0; // Calculate shipping if enabled
        $total = $subtotal + $tax_total + $shipping_total;
        
        $order_data = array(
            'subtotal' => $subtotal,
            'tax_total' => $tax_total,
            'shipping_total' => $shipping_total,
            'total' => $total,
            'payment_method' => sanitize_text_field($_POST['payment_method']),
            'billing_data' => array(
                'first_name' => sanitize_text_field($_POST['first_name']),
                'last_name' => sanitize_text_field($_POST['last_name']),
                'email' => sanitize_email($_POST['email']),
                'phone' => sanitize_text_field($_POST['phone']),
                'address' => sanitize_text_field($_POST['address']),
                'city' => sanitize_text_field($_POST['city']),
                'state' => sanitize_text_field($_POST['state'] ?? ''),
                'postcode' => sanitize_text_field($_POST['postcode'] ?? ''),
                'country' => sanitize_text_field($_POST['country'] ?? '')
            ),
            'order_notes' => sanitize_textarea_field($_POST['order_notes'] ?? ''),
            'items' => array()
        );
        
        // Prepare order items
        foreach ($cart_items as $item) {
            $order_data['items'][] = array(
                'product_id' => $item['product_id'],
                'product_name' => $item['product']->name,
                'product_sku' => $item['product']->sku,
                'quantity' => $item['quantity'],
                'price' => $item['price'],
                'total' => $item['total']
            );
        }
        
        $order_id = self::create_order($order_data);
        
        if ($order_id) {
            // Clear cart
            $cart->clear_cart();
            
            wp_send_json_success(array(
                'message' => __('Order placed successfully!', 'smartshop'),
                'order_id' => $order_id,
                'redirect_url' => add_query_arg('order_id', $order_id, home_url('/order-confirmation/'))
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to create order. Please try again.', 'smartshop')
            ));
        }
    }
    
    /**
     * AJAX handler for updating order status
     */
    public function ajax_update_order_status() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('You do not have permission to perform this action.', 'smartshop')
            ));
        }
        
        $order_id = intval($_POST['order_id']);
        $status = sanitize_text_field($_POST['status']);
        
        if (self::update_order_status($order_id, $status)) {
            wp_send_json_success(array(
                'message' => __('Order status updated successfully!', 'smartshop')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to update order status.', 'smartshop')
            ));
        }
    }
}
