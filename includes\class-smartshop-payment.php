<?php
/**
 * SmartShop Payment Class
 * 
 * Handles payment methods and processing
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Payment {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Available payment methods
     */
    private $payment_methods = array();
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_payment_methods();
        add_action('wp_ajax_smartshop_process_payment', array($this, 'process_payment'));
        add_action('wp_ajax_nopriv_smartshop_process_payment', array($this, 'process_payment'));
    }
    
    /**
     * Initialize payment methods
     */
    private function init_payment_methods() {
        $this->payment_methods = array(
            'cod' => array(
                'title' => __('Cash on Delivery', 'smartshop'),
                'description' => __('Pay with cash upon delivery.', 'smartshop'),
                'enabled' => SmartShop_Settings::get_option('enable_cod', true),
                'instructions' => SmartShop_Settings::get_option('cod_instructions', __('Pay with cash upon delivery.', 'smartshop')),
                'icon' => 'cash',
                'supports' => array('orders')
            ),
            'manual' => array(
                'title' => __('Manual Payment', 'smartshop'),
                'description' => __('Manual payment via bank transfer or other methods.', 'smartshop'),
                'enabled' => SmartShop_Settings::get_option('enable_manual', true),
                'instructions' => SmartShop_Settings::get_option('manual_instructions', __('Please transfer the amount to our bank account.', 'smartshop')),
                'icon' => 'bank',
                'supports' => array('orders')
            ),
            'stripe' => array(
                'title' => __('Credit Card (Stripe)', 'smartshop'),
                'description' => __('Pay securely with your credit card via Stripe.', 'smartshop'),
                'enabled' => SmartShop_Settings::get_option('enable_stripe', false),
                'instructions' => __('Your payment will be processed securely.', 'smartshop'),
                'icon' => 'credit-card',
                'supports' => array('orders', 'refunds')
            ),
            'paypal' => array(
                'title' => __('PayPal', 'smartshop'),
                'description' => __('Pay with your PayPal account.', 'smartshop'),
                'enabled' => SmartShop_Settings::get_option('enable_paypal', false),
                'instructions' => __('You will be redirected to PayPal to complete your payment.', 'smartshop'),
                'icon' => 'paypal',
                'supports' => array('orders', 'refunds')
            )
        );
        
        // Allow plugins to add custom payment methods
        $this->payment_methods = apply_filters('smartshop_payment_methods', $this->payment_methods);
    }
    
    /**
     * Get available payment methods
     */
    public function get_available_payment_methods() {
        $available = array();
        
        foreach ($this->payment_methods as $id => $method) {
            if ($method['enabled']) {
                $available[$id] = $method;
            }
        }
        
        return $available;
    }
    
    /**
     * Get payment method
     */
    public function get_payment_method($method_id) {
        return isset($this->payment_methods[$method_id]) ? $this->payment_methods[$method_id] : null;
    }
    
    /**
     * Process payment
     */
    public function process_payment() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        $payment_method = sanitize_text_field($_POST['payment_method'] ?? '');
        $order_id = intval($_POST['order_id'] ?? 0);
        
        if (!$payment_method || !$order_id) {
            wp_send_json_error(array(
                'message' => __('Invalid payment data.', 'smartshop')
            ));
        }
        
        $method = $this->get_payment_method($payment_method);
        if (!$method || !$method['enabled']) {
            wp_send_json_error(array(
                'message' => __('Payment method not available.', 'smartshop')
            ));
        }
        
        $order = SmartShop_Order::get_order($order_id);
        if (!$order) {
            wp_send_json_error(array(
                'message' => __('Order not found.', 'smartshop')
            ));
        }
        
        // Process payment based on method
        switch ($payment_method) {
            case 'cod':
                $result = $this->process_cod_payment($order);
                break;
                
            case 'manual':
                $result = $this->process_manual_payment($order);
                break;
                
            case 'stripe':
                $result = $this->process_stripe_payment($order);
                break;
                
            case 'paypal':
                $result = $this->process_paypal_payment($order);
                break;
                
            default:
                $result = apply_filters('smartshop_process_payment_' . $payment_method, false, $order);
                break;
        }
        
        if ($result && isset($result['success']) && $result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }
    
    /**
     * Process Cash on Delivery payment
     */
    private function process_cod_payment($order) {
        // Update order status
        SmartShop_Order::update_order_status($order->id, 'processing');
        
        // Update payment status
        global $wpdb;
        $orders_table = SmartShop_Database::get_table_name('orders');
        
        $wpdb->update(
            $orders_table,
            array(
                'payment_status' => 'pending',
                'payment_method' => 'cod'
            ),
            array('id' => $order->id),
            array('%s', '%s'),
            array('%d')
        );
        
        return array(
            'success' => true,
            'message' => __('Order placed successfully! You will pay upon delivery.', 'smartshop'),
            'redirect_url' => home_url('/order-confirmation/?order_id=' . $order->id)
        );
    }
    
    /**
     * Process Manual payment
     */
    private function process_manual_payment($order) {
        // Update order status
        SmartShop_Order::update_order_status($order->id, 'on-hold');
        
        // Update payment status
        global $wpdb;
        $orders_table = SmartShop_Database::get_table_name('orders');
        
        $wpdb->update(
            $orders_table,
            array(
                'payment_status' => 'pending',
                'payment_method' => 'manual'
            ),
            array('id' => $order->id),
            array('%s', '%s'),
            array('%d')
        );
        
        return array(
            'success' => true,
            'message' => __('Order placed successfully! Please follow the payment instructions.', 'smartshop'),
            'redirect_url' => home_url('/order-confirmation/?order_id=' . $order->id)
        );
    }
    
    /**
     * Process Stripe payment
     */
    private function process_stripe_payment($order) {
        // This would integrate with Stripe API
        // For now, return a placeholder response
        
        return array(
            'success' => false,
            'message' => __('Stripe integration not yet implemented.', 'smartshop')
        );
    }
    
    /**
     * Process PayPal payment
     */
    private function process_paypal_payment($order) {
        // This would integrate with PayPal API
        // For now, return a placeholder response
        
        return array(
            'success' => false,
            'message' => __('PayPal integration not yet implemented.', 'smartshop')
        );
    }
    
    /**
     * Get payment method icon
     */
    public function get_payment_method_icon($method_id) {
        $method = $this->get_payment_method($method_id);
        if (!$method) {
            return '';
        }
        
        $icons = array(
            'cash' => '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line></svg>',
            'bank' => '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m20 17-2 2-2-2m-2-2-2 2-2-2m8-2v6a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-6"></path><path d="m9 7 3-3 3 3v13a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1V7z"></path></svg>',
            'credit-card' => '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect><line x1="1" y1="10" x2="23" y2="10"></line></svg>',
            'paypal' => '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.076-.026.175-.041.254-.93 4.778-4.005 7.201-9.138 7.201h-2.19a.563.563 0 0 0-.556.479l-1.187 7.527h-.506l1.12-7.106c.082-.518.526-.9 1.05-.9h2.19c4.298 0 7.664-1.747 8.647-6.797.03-.149.054-.294.077-.437.195-1.24.006-2.185-.859-2.68z"/></svg>'
        );
        
        return isset($icons[$method['icon']]) ? $icons[$method['icon']] : '';
    }
    
    /**
     * Validate payment method
     */
    public function validate_payment_method($method_id, $order_data = array()) {
        $method = $this->get_payment_method($method_id);
        
        if (!$method || !$method['enabled']) {
            return new WP_Error('invalid_payment_method', __('Invalid payment method.', 'smartshop'));
        }
        
        // Additional validation can be added here for specific payment methods
        switch ($method_id) {
            case 'stripe':
                // Validate Stripe-specific requirements
                if (!SmartShop_Settings::get_option('stripe_publishable_key') || !SmartShop_Settings::get_option('stripe_secret_key')) {
                    return new WP_Error('stripe_not_configured', __('Stripe is not properly configured.', 'smartshop'));
                }
                break;
                
            case 'paypal':
                // Validate PayPal-specific requirements
                if (!SmartShop_Settings::get_option('paypal_client_id') || !SmartShop_Settings::get_option('paypal_client_secret')) {
                    return new WP_Error('paypal_not_configured', __('PayPal is not properly configured.', 'smartshop'));
                }
                break;
        }
        
        return true;
    }
    
    /**
     * Get payment instructions
     */
    public function get_payment_instructions($method_id) {
        $method = $this->get_payment_method($method_id);
        return $method ? $method['instructions'] : '';
    }
}
