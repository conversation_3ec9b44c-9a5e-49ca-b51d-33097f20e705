<?php
/**
 * SmartShop Product Class
 * 
 * Handles product operations and management
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Product {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('wp_ajax_smartshop_get_products', array($this, 'ajax_get_products'));
        add_action('wp_ajax_nopriv_smartshop_get_products', array($this, 'ajax_get_products'));
        add_action('wp_ajax_smartshop_search_products', array($this, 'ajax_search_products'));
        add_action('wp_ajax_nopriv_smartshop_search_products', array($this, 'ajax_search_products'));
    }
    
    /**
     * Create a new product
     */
    public static function create_product($data) {
        global $wpdb;
        
        $products_table = SmartShop_Database::get_table_name('products');
        
        // Sanitize data
        $product_data = array(
            'name' => sanitize_text_field($data['name']),
            'slug' => sanitize_title($data['slug'] ?: $data['name']),
            'description' => wp_kses_post($data['description'] ?? ''),
            'short_description' => sanitize_textarea_field($data['short_description'] ?? ''),
            'price' => floatval($data['price']),
            'sale_price' => isset($data['sale_price']) ? floatval($data['sale_price']) : null,
            'sku' => sanitize_text_field($data['sku'] ?? ''),
            'stock_quantity' => isset($data['stock_quantity']) ? intval($data['stock_quantity']) : null,
            'stock_status' => sanitize_text_field($data['stock_status'] ?? 'instock'),
            'manage_stock' => isset($data['manage_stock']) ? 1 : 0,
            'category_id' => isset($data['category_id']) ? intval($data['category_id']) : null,
            'image_url' => esc_url_raw($data['image_url'] ?? ''),
            'gallery' => isset($data['gallery']) ? serialize($data['gallery']) : '',
            'weight' => sanitize_text_field($data['weight'] ?? ''),
            'dimensions' => sanitize_text_field($data['dimensions'] ?? ''),
            'status' => sanitize_text_field($data['status'] ?? 'publish'),
            'featured' => isset($data['featured']) ? 1 : 0,
            'meta_data' => isset($data['meta_data']) ? serialize($data['meta_data']) : ''
        );
        
        // Ensure unique slug
        $product_data['slug'] = self::generate_unique_slug($product_data['slug']);
        
        $result = $wpdb->insert($products_table, $product_data);
        
        if ($result !== false) {
            return $wpdb->insert_id;
        }
        
        return false;
    }
    
    /**
     * Update a product
     */
    public static function update_product($product_id, $data) {
        global $wpdb;
        
        $products_table = SmartShop_Database::get_table_name('products');
        
        // Sanitize data
        $product_data = array();
        
        if (isset($data['name'])) {
            $product_data['name'] = sanitize_text_field($data['name']);
        }
        
        if (isset($data['slug'])) {
            $product_data['slug'] = self::generate_unique_slug(sanitize_title($data['slug']), $product_id);
        }
        
        if (isset($data['description'])) {
            $product_data['description'] = wp_kses_post($data['description']);
        }
        
        if (isset($data['short_description'])) {
            $product_data['short_description'] = sanitize_textarea_field($data['short_description']);
        }
        
        if (isset($data['price'])) {
            $product_data['price'] = floatval($data['price']);
        }
        
        if (isset($data['sale_price'])) {
            $product_data['sale_price'] = $data['sale_price'] !== '' ? floatval($data['sale_price']) : null;
        }
        
        if (isset($data['sku'])) {
            $product_data['sku'] = sanitize_text_field($data['sku']);
        }
        
        if (isset($data['stock_quantity'])) {
            $product_data['stock_quantity'] = $data['stock_quantity'] !== '' ? intval($data['stock_quantity']) : null;
        }
        
        if (isset($data['stock_status'])) {
            $product_data['stock_status'] = sanitize_text_field($data['stock_status']);
        }
        
        if (isset($data['manage_stock'])) {
            $product_data['manage_stock'] = $data['manage_stock'] ? 1 : 0;
        }
        
        if (isset($data['category_id'])) {
            $product_data['category_id'] = intval($data['category_id']);
        }
        
        if (isset($data['image_url'])) {
            $product_data['image_url'] = esc_url_raw($data['image_url']);
        }
        
        if (isset($data['gallery'])) {
            $product_data['gallery'] = serialize($data['gallery']);
        }
        
        if (isset($data['weight'])) {
            $product_data['weight'] = sanitize_text_field($data['weight']);
        }
        
        if (isset($data['dimensions'])) {
            $product_data['dimensions'] = sanitize_text_field($data['dimensions']);
        }
        
        if (isset($data['status'])) {
            $product_data['status'] = sanitize_text_field($data['status']);
        }
        
        if (isset($data['featured'])) {
            $product_data['featured'] = $data['featured'] ? 1 : 0;
        }
        
        if (isset($data['meta_data'])) {
            $product_data['meta_data'] = serialize($data['meta_data']);
        }
        
        if (!empty($product_data)) {
            $result = $wpdb->update(
                $products_table,
                $product_data,
                array('id' => $product_id),
                null,
                array('%d')
            );
            
            return $result !== false;
        }
        
        return false;
    }
    
    /**
     * Delete a product
     */
    public static function delete_product($product_id) {
        global $wpdb;
        
        $products_table = SmartShop_Database::get_table_name('products');
        
        $result = $wpdb->delete(
            $products_table,
            array('id' => $product_id),
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Get a product by ID
     */
    public static function get_product($product_id) {
        global $wpdb;
        
        $products_table = SmartShop_Database::get_table_name('products');
        
        $product = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $products_table WHERE id = %d",
            $product_id
        ));
        
        if ($product) {
            $product->gallery = unserialize($product->gallery);
            $product->meta_data = unserialize($product->meta_data);
            $product->category = self::get_product_category($product->category_id);
        }
        
        return $product;
    }
    
    /**
     * Get a product by slug
     */
    public static function get_product_by_slug($slug) {
        global $wpdb;
        
        $products_table = SmartShop_Database::get_table_name('products');
        
        $product = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $products_table WHERE slug = %s AND status = 'publish'",
            $slug
        ));
        
        if ($product) {
            $product->gallery = unserialize($product->gallery);
            $product->meta_data = unserialize($product->meta_data);
            $product->category = self::get_product_category($product->category_id);
        }
        
        return $product;
    }
    
    /**
     * Get products with filters
     */
    public static function get_products($args = array()) {
        global $wpdb;
        
        $defaults = array(
            'limit' => 12,
            'offset' => 0,
            'category_id' => null,
            'search' => '',
            'status' => 'publish',
            'featured' => null,
            'orderby' => 'created_at',
            'order' => 'DESC',
            'min_price' => null,
            'max_price' => null,
            'in_stock' => null
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $products_table = SmartShop_Database::get_table_name('products');
        $categories_table = SmartShop_Database::get_table_name('categories');
        
        $where_conditions = array("p.status = %s");
        $where_values = array($args['status']);
        
        if ($args['category_id']) {
            $where_conditions[] = "p.category_id = %d";
            $where_values[] = $args['category_id'];
        }
        
        if ($args['search']) {
            $where_conditions[] = "(p.name LIKE %s OR p.description LIKE %s OR p.sku LIKE %s)";
            $search_term = '%' . $wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }
        
        if ($args['featured'] !== null) {
            $where_conditions[] = "p.featured = %d";
            $where_values[] = $args['featured'] ? 1 : 0;
        }
        
        if ($args['min_price'] !== null) {
            $where_conditions[] = "p.price >= %f";
            $where_values[] = floatval($args['min_price']);
        }
        
        if ($args['max_price'] !== null) {
            $where_conditions[] = "p.price <= %f";
            $where_values[] = floatval($args['max_price']);
        }
        
        if ($args['in_stock']) {
            $where_conditions[] = "p.stock_status = 'instock'";
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $orderby = sanitize_sql_orderby($args['orderby']);
        $order = strtoupper($args['order']) === 'ASC' ? 'ASC' : 'DESC';
        
        $sql = "SELECT p.*, c.name as category_name 
                FROM $products_table p 
                LEFT JOIN $categories_table c ON p.category_id = c.id 
                WHERE $where_clause 
                ORDER BY p.$orderby $order 
                LIMIT %d OFFSET %d";
        
        $where_values[] = intval($args['limit']);
        $where_values[] = intval($args['offset']);
        
        $query = $wpdb->prepare($sql, $where_values);
        $products = $wpdb->get_results($query);
        
        foreach ($products as $product) {
            $product->gallery = unserialize($product->gallery);
            $product->meta_data = unserialize($product->meta_data);
        }
        
        return $products;
    }
    
    /**
     * Get product count
     */
    public static function get_product_count($args = array()) {
        global $wpdb;
        
        $defaults = array(
            'category_id' => null,
            'search' => '',
            'status' => 'publish',
            'featured' => null,
            'min_price' => null,
            'max_price' => null,
            'in_stock' => null
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $products_table = SmartShop_Database::get_table_name('products');
        
        $where_conditions = array("status = %s");
        $where_values = array($args['status']);
        
        if ($args['category_id']) {
            $where_conditions[] = "category_id = %d";
            $where_values[] = $args['category_id'];
        }
        
        if ($args['search']) {
            $where_conditions[] = "(name LIKE %s OR description LIKE %s OR sku LIKE %s)";
            $search_term = '%' . $wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }
        
        if ($args['featured'] !== null) {
            $where_conditions[] = "featured = %d";
            $where_values[] = $args['featured'] ? 1 : 0;
        }
        
        if ($args['min_price'] !== null) {
            $where_conditions[] = "price >= %f";
            $where_values[] = floatval($args['min_price']);
        }
        
        if ($args['max_price'] !== null) {
            $where_conditions[] = "price <= %f";
            $where_values[] = floatval($args['max_price']);
        }
        
        if ($args['in_stock']) {
            $where_conditions[] = "stock_status = 'instock'";
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $sql = "SELECT COUNT(*) FROM $products_table WHERE $where_clause";
        $query = $wpdb->prepare($sql, $where_values);
        
        return intval($wpdb->get_var($query));
    }
    
    /**
     * Generate unique slug
     */
    private static function generate_unique_slug($slug, $exclude_id = null) {
        global $wpdb;
        
        $products_table = SmartShop_Database::get_table_name('products');
        $original_slug = $slug;
        $counter = 1;
        
        while (true) {
            $where_clause = "slug = %s";
            $where_values = array($slug);
            
            if ($exclude_id) {
                $where_clause .= " AND id != %d";
                $where_values[] = $exclude_id;
            }
            
            $query = $wpdb->prepare("SELECT id FROM $products_table WHERE $where_clause", $where_values);
            $exists = $wpdb->get_var($query);
            
            if (!$exists) {
                break;
            }
            
            $slug = $original_slug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    /**
     * Get product category
     */
    private static function get_product_category($category_id) {
        if (!$category_id) {
            return null;
        }
        
        global $wpdb;
        $categories_table = SmartShop_Database::get_table_name('categories');
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $categories_table WHERE id = %d",
            $category_id
        ));
    }
    
    /**
     * AJAX handler for getting products
     */
    public function ajax_get_products() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        $args = array(
            'limit' => intval($_POST['limit'] ?? 12),
            'offset' => intval($_POST['offset'] ?? 0),
            'category_id' => intval($_POST['category_id'] ?? 0) ?: null,
            'search' => sanitize_text_field($_POST['search'] ?? ''),
            'orderby' => sanitize_text_field($_POST['orderby'] ?? 'created_at'),
            'order' => sanitize_text_field($_POST['order'] ?? 'DESC'),
            'min_price' => floatval($_POST['min_price'] ?? 0) ?: null,
            'max_price' => floatval($_POST['max_price'] ?? 0) ?: null,
            'in_stock' => isset($_POST['in_stock']) ? (bool)$_POST['in_stock'] : null
        );
        
        $products = self::get_products($args);
        $total = self::get_product_count($args);
        
        wp_send_json_success(array(
            'products' => $products,
            'total' => $total,
            'has_more' => ($args['offset'] + $args['limit']) < $total
        ));
    }
    
    /**
     * AJAX handler for searching products
     */
    public function ajax_search_products() {
        check_ajax_referer('smartshop_nonce', 'nonce');
        
        $search = sanitize_text_field($_POST['search'] ?? '');
        $limit = intval($_POST['limit'] ?? 10);
        
        $products = self::get_products(array(
            'search' => $search,
            'limit' => $limit,
            'orderby' => 'name',
            'order' => 'ASC'
        ));
        
        wp_send_json_success($products);
    }
}
