<?php
/**
 * SmartShop Settings Class
 * 
 * Handles plugin settings and options
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Settings {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Settings cache
     */
    private static $settings_cache = array();
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Load settings into cache
        $this->load_settings();
    }
    
    /**
     * Load all settings into cache
     */
    private function load_settings() {
        global $wpdb;
        
        $settings_table = SmartShop_Database::get_table_name('settings');
        $results = $wpdb->get_results("SELECT option_name, option_value FROM $settings_table");
        
        foreach ($results as $row) {
            self::$settings_cache[$row->option_name] = maybe_unserialize($row->option_value);
        }
    }
    
    /**
     * Get option value
     */
    public static function get_option($option_name, $default = false) {
        if (isset(self::$settings_cache[$option_name])) {
            return self::$settings_cache[$option_name];
        }
        
        global $wpdb;
        $settings_table = SmartShop_Database::get_table_name('settings');
        
        $value = $wpdb->get_var($wpdb->prepare(
            "SELECT option_value FROM $settings_table WHERE option_name = %s",
            $option_name
        ));
        
        if ($value !== null) {
            $value = maybe_unserialize($value);
            self::$settings_cache[$option_name] = $value;
            return $value;
        }
        
        return $default;
    }
    
    /**
     * Update option value
     */
    public static function update_option($option_name, $option_value) {
        global $wpdb;
        
        $settings_table = SmartShop_Database::get_table_name('settings');
        $option_value = maybe_serialize($option_value);
        
        $result = $wpdb->replace(
            $settings_table,
            array(
                'option_name' => $option_name,
                'option_value' => $option_value
            ),
            array('%s', '%s')
        );
        
        if ($result !== false) {
            self::$settings_cache[$option_name] = maybe_unserialize($option_value);
        }
        
        return $result !== false;
    }
    
    /**
     * Delete option
     */
    public static function delete_option($option_name) {
        global $wpdb;
        
        $settings_table = SmartShop_Database::get_table_name('settings');
        
        $result = $wpdb->delete(
            $settings_table,
            array('option_name' => $option_name),
            array('%s')
        );
        
        if ($result !== false) {
            unset(self::$settings_cache[$option_name]);
        }
        
        return $result !== false;
    }
    
    /**
     * Set default options
     */
    public static function set_default_options() {
        $defaults = array(
            // General Settings
            'shop_name' => get_bloginfo('name') . ' Shop',
            'shop_logo' => '',
            'primary_color' => '#3b82f6',
            'secondary_color' => '#1e40af',
            'accent_color' => '#f59e0b',
            'layout_style' => 'grid',
            'products_per_page' => 12,
            'enable_search' => 1,
            'enable_filters' => 1,
            
            // Currency Settings
            'currency' => 'USD',
            'currency_symbol' => '$',
            'currency_position' => 'left',
            'decimal_places' => 2,
            'thousand_separator' => ',',
            'decimal_separator' => '.',
            
            // Payment Settings
            'enable_cod' => 1,
            'enable_manual_payment' => 1,
            'enable_stripe' => 0,
            'enable_paypal' => 0,
            'cod_instructions' => __('Pay with cash upon delivery.', 'smartshop'),
            'manual_payment_instructions' => __('Please transfer the amount to our bank account and send us the receipt.', 'smartshop'),
            
            // User Settings
            'enable_user_registration' => 1,
            'enable_guest_checkout' => 1,
            'require_account_creation' => 0,
            
            // Email Settings
            'admin_email' => get_option('admin_email'),
            'from_email' => get_option('admin_email'),
            'from_name' => get_bloginfo('name'),
            'enable_order_emails' => 1,
            
            // Shipping Settings
            'enable_shipping' => 0,
            'shipping_cost' => 0,
            'free_shipping_threshold' => 0,
            
            // Tax Settings
            'enable_tax' => 0,
            'tax_rate' => 0,
            'tax_inclusive' => 0,
            
            // Inventory Settings
            'manage_stock' => 1,
            'low_stock_threshold' => 5,
            'out_of_stock_visibility' => 'visible',
            
            // SEO Settings
            'shop_page_title' => __('Shop', 'smartshop'),
            'shop_page_description' => __('Browse our amazing products', 'smartshop'),
            
            // Advanced Settings
            'enable_reviews' => 1,
            'enable_wishlist' => 1,
            'enable_compare' => 0,
            'enable_quick_view' => 1,
            'enable_ajax_cart' => 1,
            'cart_redirect_after_add' => 0,
            
            // Performance Settings
            'enable_cache' => 1,
            'image_quality' => 85,
            'lazy_load_images' => 1,
            
            // Security Settings
            'enable_captcha' => 0,
            'max_login_attempts' => 5,
            
            // Appearance Settings
            'show_breadcrumbs' => 1,
            'show_product_count' => 1,
            'show_sorting' => 1,
            'default_sorting' => 'menu_order',
            'grid_columns_desktop' => 4,
            'grid_columns_tablet' => 3,
            'grid_columns_mobile' => 2,
            
            // Social Settings
            'enable_social_sharing' => 1,
            'facebook_url' => '',
            'twitter_url' => '',
            'instagram_url' => '',
            
            // Maintenance
            'maintenance_mode' => 0,
            'maintenance_message' => __('We are currently updating our shop. Please check back soon!', 'smartshop'),
        );
        
        foreach ($defaults as $option_name => $option_value) {
            if (self::get_option($option_name) === false) {
                self::update_option($option_name, $option_value);
            }
        }
    }
    
    /**
     * Remove all plugin options
     */
    public static function remove_all_options() {
        global $wpdb;
        
        $settings_table = SmartShop_Database::get_table_name('settings');
        $wpdb->query("DELETE FROM $settings_table");
        
        // Clear cache
        self::$settings_cache = array();
    }
    
    /**
     * Get all settings
     */
    public static function get_all_settings() {
        return self::$settings_cache;
    }
    
    /**
     * Get settings by group
     */
    public static function get_settings_group($group) {
        $settings = array();
        
        foreach (self::$settings_cache as $key => $value) {
            if (strpos($key, $group . '_') === 0) {
                $settings[$key] = $value;
            }
        }
        
        return $settings;
    }
    
    /**
     * Update multiple options at once
     */
    public static function update_options($options) {
        foreach ($options as $option_name => $option_value) {
            self::update_option($option_name, $option_value);
        }
    }
    
    /**
     * Get formatted currency
     */
    public static function format_currency($amount) {
        $currency_symbol = self::get_option('currency_symbol', '$');
        $currency_position = self::get_option('currency_position', 'left');
        $decimal_places = self::get_option('decimal_places', 2);
        $thousand_separator = self::get_option('thousand_separator', ',');
        $decimal_separator = self::get_option('decimal_separator', '.');
        
        $formatted_amount = number_format($amount, $decimal_places, $decimal_separator, $thousand_separator);
        
        if ($currency_position === 'left') {
            return $currency_symbol . $formatted_amount;
        } else {
            return $formatted_amount . $currency_symbol;
        }
    }
}
