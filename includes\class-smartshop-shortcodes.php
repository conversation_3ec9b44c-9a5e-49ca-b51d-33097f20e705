<?php
/**
 * SmartShop Shortcodes Class
 * 
 * Handles all plugin shortcodes
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Shortcodes {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_shortcodes();
    }
    
    /**
     * Initialize shortcodes
     */
    private function init_shortcodes() {
        add_shortcode('smartshop_products', array($this, 'products_shortcode'));
        add_shortcode('smartshop_product', array($this, 'single_product_shortcode'));
        add_shortcode('smartshop_cart', array($this, 'cart_shortcode'));
        add_shortcode('smartshop_checkout', array($this, 'checkout_shortcode'));
        add_shortcode('smartshop_categories', array($this, 'categories_shortcode'));
        add_shortcode('smartshop_featured_products', array($this, 'featured_products_shortcode'));
        add_shortcode('smartshop_search', array($this, 'search_shortcode'));
        add_shortcode('smartshop_account', array($this, 'account_shortcode'));
        add_shortcode('smartshop_recent_products', array($this, 'recent_products_shortcode'));
        add_shortcode('smartshop_sale_products', array($this, 'sale_products_shortcode'));
        add_shortcode('smartshop_cart_count', array($this, 'cart_count_shortcode'));
        add_shortcode('smartshop_cart_total', array($this, 'cart_total_shortcode'));
        add_shortcode('smartshop_add_to_cart', array($this, 'add_to_cart_shortcode'));
        add_shortcode('smartshop_product_price', array($this, 'product_price_shortcode'));
        add_shortcode('smartshop_breadcrumbs', array($this, 'breadcrumbs_shortcode'));
    }
    
    /**
     * Products shortcode
     */
    public function products_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => 12,
            'category' => '',
            'featured' => '',
            'orderby' => 'created_at',
            'order' => 'DESC',
            'columns' => 4,
            'show_filters' => 'true',
            'show_search' => 'true',
            'show_pagination' => 'true'
        ), $atts, 'smartshop_products');
        
        ob_start();
        include SMARTSHOP_PLUGIN_PATH . 'templates/shortcodes/products.php';
        return ob_get_clean();
    }
    
    /**
     * Single product shortcode
     */
    public function single_product_shortcode($atts) {
        $atts = shortcode_atts(array(
            'id' => '',
            'slug' => ''
        ), $atts, 'smartshop_product');
        
        $product = null;
        
        if ($atts['id']) {
            $product = SmartShop_Product::get_product(intval($atts['id']));
        } elseif ($atts['slug']) {
            $product = SmartShop_Product::get_product_by_slug($atts['slug']);
        }
        
        if (!$product) {
            return '<p>' . __('Product not found.', 'smartshop') . '</p>';
        }
        
        ob_start();
        include SMARTSHOP_PLUGIN_PATH . 'templates/shortcodes/single-product.php';
        return ob_get_clean();
    }
    
    /**
     * Cart shortcode
     */
    public function cart_shortcode($atts) {
        $atts = shortcode_atts(array(
            'show_continue_shopping' => 'true',
            'continue_shopping_url' => ''
        ), $atts, 'smartshop_cart');
        
        ob_start();
        include SMARTSHOP_PLUGIN_PATH . 'templates/shortcodes/cart.php';
        return ob_get_clean();
    }
    
    /**
     * Checkout shortcode
     */
    public function checkout_shortcode($atts) {
        $atts = shortcode_atts(array(
            'show_order_review' => 'true',
            'require_account' => ''
        ), $atts, 'smartshop_checkout');
        
        $cart = SmartShop_Cart::get_instance();
        
        if ($cart->is_empty()) {
            return '<p>' . __('Your cart is empty. <a href="' . home_url('/shop/') . '">Continue shopping</a>', 'smartshop') . '</p>';
        }
        
        ob_start();
        include SMARTSHOP_PLUGIN_PATH . 'templates/shortcodes/checkout.php';
        return ob_get_clean();
    }
    
    /**
     * Categories shortcode
     */
    public function categories_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => 10,
            'orderby' => 'name',
            'order' => 'ASC',
            'show_count' => 'true',
            'show_images' => 'true',
            'columns' => 4
        ), $atts, 'smartshop_categories');
        
        ob_start();
        include SMARTSHOP_PLUGIN_PATH . 'templates/shortcodes/categories.php';
        return ob_get_clean();
    }
    
    /**
     * Featured products shortcode
     */
    public function featured_products_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => 8,
            'columns' => 4,
            'orderby' => 'created_at',
            'order' => 'DESC'
        ), $atts, 'smartshop_featured_products');
        
        $products = SmartShop_Product::get_products(array(
            'limit' => intval($atts['limit']),
            'featured' => 1,
            'orderby' => $atts['orderby'],
            'order' => $atts['order']
        ));
        
        ob_start();
        include SMARTSHOP_PLUGIN_PATH . 'templates/shortcodes/featured-products.php';
        return ob_get_clean();
    }
    
    /**
     * Search shortcode
     */
    public function search_shortcode($atts) {
        $atts = shortcode_atts(array(
            'placeholder' => __('Search products...', 'smartshop'),
            'show_categories' => 'true',
            'show_button' => 'true'
        ), $atts, 'smartshop_search');
        
        ob_start();
        include SMARTSHOP_PLUGIN_PATH . 'templates/shortcodes/search.php';
        return ob_get_clean();
    }
    
    /**
     * Account shortcode
     */
    public function account_shortcode($atts) {
        $atts = shortcode_atts(array(
            'show_orders' => 'true',
            'show_profile' => 'true',
            'orders_limit' => 10
        ), $atts, 'smartshop_account');
        
        if (!is_user_logged_in()) {
            return $this->get_login_form();
        }
        
        ob_start();
        include SMARTSHOP_PLUGIN_PATH . 'templates/shortcodes/account.php';
        return ob_get_clean();
    }
    
    /**
     * Get login form
     */
    private function get_login_form() {
        ob_start();
        ?>
        <div class="smartshop-login-form">
            <h3><?php _e('Login to Your Account', 'smartshop'); ?></h3>
            <?php wp_login_form(array(
                'redirect' => get_permalink(),
                'form_id' => 'smartshop-loginform',
                'label_username' => __('Username or Email', 'smartshop'),
                'label_password' => __('Password', 'smartshop'),
                'label_remember' => __('Remember Me', 'smartshop'),
                'label_log_in' => __('Log In', 'smartshop'),
                'remember' => true
            )); ?>
            
            <?php if (SmartShop_Settings::get_option('enable_user_registration', 1)): ?>
            <p class="smartshop-register-link">
                <?php _e("Don't have an account?", 'smartshop'); ?>
                <a href="<?php echo wp_registration_url(); ?>"><?php _e('Register here', 'smartshop'); ?></a>
            </p>
            <?php endif; ?>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get categories for shortcode
     */
    public static function get_categories($args = array()) {
        global $wpdb;
        
        $defaults = array(
            'limit' => 10,
            'orderby' => 'name',
            'order' => 'ASC',
            'parent_id' => 0
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $categories_table = SmartShop_Database::get_table_name('categories');
        
        $orderby = sanitize_sql_orderby($args['orderby']);
        $order = strtoupper($args['order']) === 'ASC' ? 'ASC' : 'DESC';
        
        $sql = "SELECT * FROM $categories_table 
                WHERE status = 'active' AND parent_id = %d 
                ORDER BY $orderby $order 
                LIMIT %d";
        
        $query = $wpdb->prepare($sql, $args['parent_id'], intval($args['limit']));
        
        return $wpdb->get_results($query);
    }
    
    /**
     * Get category product count
     */
    public static function get_category_product_count($category_id) {
        global $wpdb;
        
        $products_table = SmartShop_Database::get_table_name('products');
        
        return intval($wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $products_table WHERE category_id = %d AND status = 'publish'",
            $category_id
        )));
    }
    
    /**
     * Format product price
     */
    public static function format_product_price($product) {
        $price_html = '';
        
        if ($product->sale_price !== null && $product->sale_price < $product->price) {
            $price_html .= '<span class="smartshop-price-sale">' . SmartShop_Settings::format_currency($product->sale_price) . '</span>';
            $price_html .= ' <span class="smartshop-price-regular">' . SmartShop_Settings::format_currency($product->price) . '</span>';
        } else {
            $price_html .= '<span class="smartshop-price">' . SmartShop_Settings::format_currency($product->price) . '</span>';
        }
        
        return $price_html;
    }
    
    /**
     * Get product stock status
     */
    public static function get_product_stock_status($product) {
        if ($product->stock_status === 'outofstock') {
            return '<span class="smartshop-stock-status out-of-stock">' . __('Out of Stock', 'smartshop') . '</span>';
        } elseif ($product->manage_stock && $product->stock_quantity !== null) {
            if ($product->stock_quantity <= SmartShop_Settings::get_option('low_stock_threshold', 5)) {
                return '<span class="smartshop-stock-status low-stock">' . sprintf(__('Only %d left in stock', 'smartshop'), $product->stock_quantity) . '</span>';
            } else {
                return '<span class="smartshop-stock-status in-stock">' . __('In Stock', 'smartshop') . '</span>';
            }
        } else {
            return '<span class="smartshop-stock-status in-stock">' . __('In Stock', 'smartshop') . '</span>';
        }
    }
    
    /**
     * Get add to cart button
     */
    public static function get_add_to_cart_button($product, $classes = '') {
        $button_text = __('Add to Cart', 'smartshop');
        $button_classes = 'smartshop-add-to-cart-btn ' . $classes;
        
        if ($product->stock_status === 'outofstock') {
            $button_text = __('Out of Stock', 'smartshop');
            $button_classes .= ' disabled';
            $disabled = 'disabled';
        } else {
            $disabled = '';
        }
        
        return sprintf(
            '<button type="button" class="%s" data-product-id="%d" %s>%s</button>',
            esc_attr($button_classes),
            $product->id,
            $disabled,
            esc_html($button_text)
        );
    }
    
    /**
     * Get product image
     */
    public static function get_product_image($product, $size = 'medium') {
        if ($product->image_url) {
            return sprintf(
                '<img src="%s" alt="%s" class="smartshop-product-image">',
                esc_url($product->image_url),
                esc_attr($product->name)
            );
        } else {
            return sprintf(
                '<div class="smartshop-product-image-placeholder">
                    <span>%s</span>
                </div>',
                __('No Image', 'smartshop')
            );
        }
    }
    
    /**
     * Get product rating (placeholder for future rating system)
     */
    public static function get_product_rating($product) {
        // Placeholder for rating system
        return '';
    }
    
    /**
     * Get breadcrumbs
     */
    public static function get_breadcrumbs($items = array()) {
        if (empty($items)) {
            return '';
        }
        
        $breadcrumbs = '<nav class="smartshop-breadcrumbs" aria-label="' . __('Breadcrumb', 'smartshop') . '">';
        $breadcrumbs .= '<ol class="breadcrumb-list">';
        
        foreach ($items as $index => $item) {
            $is_last = ($index === count($items) - 1);
            
            $breadcrumbs .= '<li class="breadcrumb-item' . ($is_last ? ' active' : '') . '">';
            
            if (!$is_last && isset($item['url'])) {
                $breadcrumbs .= '<a href="' . esc_url($item['url']) . '">' . esc_html($item['title']) . '</a>';
            } else {
                $breadcrumbs .= '<span>' . esc_html($item['title']) . '</span>';
            }
            
            $breadcrumbs .= '</li>';
        }
        
        $breadcrumbs .= '</ol>';
        $breadcrumbs .= '</nav>';
        
        return $breadcrumbs;
    }

    /**
     * Recent products shortcode
     */
    public function recent_products_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => 8,
            'columns' => 4,
            'orderby' => 'created_at',
            'order' => 'DESC'
        ), $atts, 'smartshop_recent_products');

        $products = SmartShop_Product::get_products(array(
            'limit' => intval($atts['limit']),
            'orderby' => $atts['orderby'],
            'order' => $atts['order']
        ));

        ob_start();
        include SMARTSHOP_PLUGIN_PATH . 'templates/shortcodes/recent-products.php';
        return ob_get_clean();
    }

    /**
     * Sale products shortcode
     */
    public function sale_products_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => 8,
            'columns' => 4,
            'orderby' => 'created_at',
            'order' => 'DESC'
        ), $atts, 'smartshop_sale_products');

        $products = SmartShop_Product::get_products(array(
            'limit' => intval($atts['limit']),
            'on_sale' => true,
            'orderby' => $atts['orderby'],
            'order' => $atts['order']
        ));

        ob_start();
        include SMARTSHOP_PLUGIN_PATH . 'templates/shortcodes/sale-products.php';
        return ob_get_clean();
    }

    /**
     * Cart count shortcode
     */
    public function cart_count_shortcode($atts) {
        $cart = SmartShop_Cart::get_instance();
        return '<span class="smartshop-cart-count">' . $cart->get_cart_count() . '</span>';
    }

    /**
     * Cart total shortcode
     */
    public function cart_total_shortcode($atts) {
        $cart = SmartShop_Cart::get_instance();
        return '<span class="smartshop-cart-total">' . SmartShop_Settings::format_currency($cart->get_cart_total()) . '</span>';
    }

    /**
     * Add to cart shortcode
     */
    public function add_to_cart_shortcode($atts) {
        $atts = shortcode_atts(array(
            'id' => '',
            'text' => __('Add to Cart', 'smartshop'),
            'class' => 'btn btn-primary'
        ), $atts, 'smartshop_add_to_cart');

        if (!$atts['id']) {
            return '<p>' . __('Product ID is required.', 'smartshop') . '</p>';
        }

        $product = SmartShop_Product::get_product(intval($atts['id']));
        if (!$product) {
            return '<p>' . __('Product not found.', 'smartshop') . '</p>';
        }

        return self::get_add_to_cart_button($product, $atts['class']);
    }

    /**
     * Product price shortcode
     */
    public function product_price_shortcode($atts) {
        $atts = shortcode_atts(array(
            'id' => '',
            'format' => 'full'
        ), $atts, 'smartshop_product_price');

        if (!$atts['id']) {
            return '<p>' . __('Product ID is required.', 'smartshop') . '</p>';
        }

        $product = SmartShop_Product::get_product(intval($atts['id']));
        if (!$product) {
            return '<p>' . __('Product not found.', 'smartshop') . '</p>';
        }

        return '<span class="smartshop-product-price">' . self::format_product_price($product) . '</span>';
    }

    /**
     * Breadcrumbs shortcode
     */
    public function breadcrumbs_shortcode($atts) {
        $atts = shortcode_atts(array(
            'separator' => '/',
            'home_text' => __('Home', 'smartshop')
        ), $atts, 'smartshop_breadcrumbs');

        $breadcrumbs = array(
            array(
                'title' => $atts['home_text'],
                'url' => home_url('/')
            )
        );

        // Add current page to breadcrumbs
        if (is_page()) {
            $breadcrumbs[] = array(
                'title' => get_the_title(),
                'url' => ''
            );
        }

        return self::get_breadcrumbs($breadcrumbs);
    }
}
