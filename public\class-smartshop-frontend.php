<?php
/**
 * SmartShop Frontend Class
 * 
 * Handles frontend display and functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Frontend {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_head', array($this, 'add_meta_tags'));
        add_filter('body_class', array($this, 'add_body_classes'));
    }
    
    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_scripts() {
        // Only load on SmartShop pages or when shortcodes are present
        if ($this->should_load_assets()) {
            // Enqueue styles
            wp_enqueue_style(
                'smartshop-frontend',
                SMARTSHOP_PLUGIN_URL . 'assets/css/frontend.css',
                array(),
                SMARTSHOP_VERSION
            );
            
            // Enqueue scripts
            wp_enqueue_script(
                'smartshop-frontend',
                SMARTSHOP_PLUGIN_URL . 'assets/js/frontend.js',
                array('jquery'),
                SMARTSHOP_VERSION,
                true
            );
            
            // Localize script
            wp_localize_script('smartshop-frontend', 'smartshop_frontend', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('smartshop_nonce'),
                'currency_symbol' => SmartShop_Settings::get_option('currency_symbol', '$'),
                'currency_position' => SmartShop_Settings::get_option('currency_position', 'left'),
                'cart_url' => SmartShop_Public::get_cart_url(),
                'checkout_url' => SmartShop_Public::get_checkout_url(),
                'shop_url' => SmartShop_Public::get_shop_url(),
                'enable_ajax_cart' => SmartShop_Settings::get_option('enable_ajax_cart', 1),
                'cart_redirect_after_add' => SmartShop_Settings::get_option('cart_redirect_after_add', 0),
                'messages' => array(
                    'added_to_cart' => __('Product added to cart!', 'smartshop'),
                    'removed_from_cart' => __('Product removed from cart!', 'smartshop'),
                    'cart_updated' => __('Cart updated!', 'smartshop'),
                    'cart_cleared' => __('Cart cleared!', 'smartshop'),
                    'loading' => __('Loading...', 'smartshop'),
                    'error' => __('An error occurred. Please try again.', 'smartshop'),
                    'confirm_remove' => __('Are you sure you want to remove this item?', 'smartshop'),
                    'confirm_clear' => __('Are you sure you want to clear your cart?', 'smartshop'),
                    'out_of_stock' => __('This product is out of stock.', 'smartshop'),
                    'invalid_quantity' => __('Please enter a valid quantity.', 'smartshop')
                )
            ));
        }
    }
    
    /**
     * Check if assets should be loaded
     */
    private function should_load_assets() {
        global $post;
        
        // Load on SmartShop pages
        if (SmartShop_Public::is_smartshop_page()) {
            return true;
        }
        
        // Load if page contains SmartShop shortcodes
        if ($post && has_shortcode($post->post_content, 'smartshop_products')) {
            return true;
        }
        
        if ($post && has_shortcode($post->post_content, 'smartshop_cart')) {
            return true;
        }
        
        if ($post && has_shortcode($post->post_content, 'smartshop_checkout')) {
            return true;
        }
        
        // Load if widget contains SmartShop content (future feature)
        
        return false;
    }
    
    /**
     * Add meta tags to head
     */
    public function add_meta_tags() {
        if (SmartShop_Public::is_product_page()) {
            global $smartshop_product;
            
            if ($smartshop_product) {
                echo '<meta property="og:title" content="' . esc_attr($smartshop_product->name) . '">' . "\n";
                echo '<meta property="og:description" content="' . esc_attr(wp_strip_all_tags($smartshop_product->short_description)) . '">' . "\n";
                echo '<meta property="og:type" content="product">' . "\n";
                echo '<meta property="og:url" content="' . esc_url(SmartShop_Public::get_product_url($smartshop_product->slug)) . '">' . "\n";
                
                if ($smartshop_product->image_url) {
                    echo '<meta property="og:image" content="' . esc_url($smartshop_product->image_url) . '">' . "\n";
                }
                
                echo '<meta property="product:price:amount" content="' . esc_attr($smartshop_product->price) . '">' . "\n";
                echo '<meta property="product:price:currency" content="' . esc_attr(SmartShop_Settings::get_option('currency', 'USD')) . '">' . "\n";
            }
        }
    }
    
    /**
     * Add body classes
     */
    public function add_body_classes($classes) {
        if (SmartShop_Public::is_smartshop_page()) {
            $classes[] = 'smartshop-page';
            
            $page = get_query_var('smartshop_page');
            $classes[] = 'smartshop-' . $page;
            
            // Add layout class
            $layout = SmartShop_Settings::get_option('layout_style', 'grid');
            $classes[] = 'smartshop-layout-' . $layout;
        }
        
        return $classes;
    }
    
    /**
     * Get product grid HTML
     */
    public static function get_product_grid($products, $columns = 4) {
        if (empty($products)) {
            return '<p class="smartshop-no-products">' . __('No products found.', 'smartshop') . '</p>';
        }
        
        $grid_class = 'smartshop-product-grid smartshop-columns-' . intval($columns);
        
        ob_start();
        ?>
        <div class="<?php echo esc_attr($grid_class); ?>">
            <?php foreach ($products as $product): ?>
                <div class="smartshop-product-item" data-product-id="<?php echo esc_attr($product->id); ?>">
                    <?php echo self::get_product_card($product); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get product card HTML
     */
    public static function get_product_card($product) {
        ob_start();
        ?>
        <div class="smartshop-product-card">
            <div class="product-image">
                <a href="<?php echo esc_url(SmartShop_Public::get_product_url($product->slug)); ?>">
                    <?php echo SmartShop_Shortcodes::get_product_image($product); ?>
                </a>
                
                <?php if ($product->featured): ?>
                    <span class="product-badge featured"><?php _e('Featured', 'smartshop'); ?></span>
                <?php endif; ?>
                
                <?php if ($product->sale_price !== null && $product->sale_price < $product->price): ?>
                    <?php $discount = round((($product->price - $product->sale_price) / $product->price) * 100); ?>
                    <span class="product-badge sale"><?php echo sprintf(__('%d%% OFF', 'smartshop'), $discount); ?></span>
                <?php endif; ?>
            </div>
            
            <div class="product-info">
                <h3 class="product-title">
                    <a href="<?php echo esc_url(SmartShop_Public::get_product_url($product->slug)); ?>">
                        <?php echo esc_html($product->name); ?>
                    </a>
                </h3>
                
                <?php if ($product->category_name): ?>
                    <div class="product-category">
                        <?php echo esc_html($product->category_name); ?>
                    </div>
                <?php endif; ?>
                
                <div class="product-price">
                    <?php echo SmartShop_Shortcodes::format_product_price($product); ?>
                </div>
                
                <div class="product-stock">
                    <?php echo SmartShop_Shortcodes::get_product_stock_status($product); ?>
                </div>
                
                <div class="product-actions">
                    <?php echo SmartShop_Shortcodes::get_add_to_cart_button($product, 'btn btn-primary'); ?>
                    
                    <a href="<?php echo esc_url(SmartShop_Public::get_product_url($product->slug)); ?>" 
                       class="btn btn-secondary view-product">
                        <?php _e('View Details', 'smartshop'); ?>
                    </a>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get pagination HTML
     */
    public static function get_pagination($current_page, $total_pages, $base_url = '') {
        if ($total_pages <= 1) {
            return '';
        }
        
        $pagination = '<nav class="smartshop-pagination" aria-label="' . __('Products pagination', 'smartshop') . '">';
        $pagination .= '<ul class="pagination-list">';
        
        // Previous page
        if ($current_page > 1) {
            $prev_url = add_query_arg('page', $current_page - 1, $base_url);
            $pagination .= '<li class="pagination-item prev">';
            $pagination .= '<a href="' . esc_url($prev_url) . '" class="pagination-link">' . __('Previous', 'smartshop') . '</a>';
            $pagination .= '</li>';
        }
        
        // Page numbers
        $start = max(1, $current_page - 2);
        $end = min($total_pages, $current_page + 2);
        
        if ($start > 1) {
            $pagination .= '<li class="pagination-item">';
            $pagination .= '<a href="' . esc_url(add_query_arg('page', 1, $base_url)) . '" class="pagination-link">1</a>';
            $pagination .= '</li>';
            
            if ($start > 2) {
                $pagination .= '<li class="pagination-item dots"><span>...</span></li>';
            }
        }
        
        for ($i = $start; $i <= $end; $i++) {
            $pagination .= '<li class="pagination-item' . ($i === $current_page ? ' active' : '') . '">';
            
            if ($i === $current_page) {
                $pagination .= '<span class="pagination-link current">' . $i . '</span>';
            } else {
                $pagination .= '<a href="' . esc_url(add_query_arg('page', $i, $base_url)) . '" class="pagination-link">' . $i . '</a>';
            }
            
            $pagination .= '</li>';
        }
        
        if ($end < $total_pages) {
            if ($end < $total_pages - 1) {
                $pagination .= '<li class="pagination-item dots"><span>...</span></li>';
            }
            
            $pagination .= '<li class="pagination-item">';
            $pagination .= '<a href="' . esc_url(add_query_arg('page', $total_pages, $base_url)) . '" class="pagination-link">' . $total_pages . '</a>';
            $pagination .= '</li>';
        }
        
        // Next page
        if ($current_page < $total_pages) {
            $next_url = add_query_arg('page', $current_page + 1, $base_url);
            $pagination .= '<li class="pagination-item next">';
            $pagination .= '<a href="' . esc_url($next_url) . '" class="pagination-link">' . __('Next', 'smartshop') . '</a>';
            $pagination .= '</li>';
        }
        
        $pagination .= '</ul>';
        $pagination .= '</nav>';
        
        return $pagination;
    }
    
    /**
     * Get search form HTML
     */
    public static function get_search_form($placeholder = '', $show_categories = true) {
        if (empty($placeholder)) {
            $placeholder = __('Search products...', 'smartshop');
        }
        
        ob_start();
        ?>
        <form class="smartshop-search-form" method="get" action="<?php echo esc_url(SmartShop_Public::get_shop_url()); ?>">
            <div class="search-input-group">
                <input type="text" 
                       name="search" 
                       class="search-input" 
                       placeholder="<?php echo esc_attr($placeholder); ?>"
                       value="<?php echo esc_attr($_GET['search'] ?? ''); ?>">
                
                <?php if ($show_categories): ?>
                    <select name="category" class="search-category">
                        <option value=""><?php _e('All Categories', 'smartshop'); ?></option>
                        <?php
                        $categories = SmartShop_Shortcodes::get_categories();
                        foreach ($categories as $category):
                        ?>
                            <option value="<?php echo esc_attr($category->id); ?>" 
                                    <?php selected($_GET['category'] ?? '', $category->id); ?>>
                                <?php echo esc_html($category->name); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                <?php endif; ?>
                
                <button type="submit" class="search-button">
                    <span class="search-icon">🔍</span>
                    <span class="search-text"><?php _e('Search', 'smartshop'); ?></span>
                </button>
            </div>
        </form>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get filters sidebar HTML
     */
    public static function get_filters_sidebar() {
        ob_start();
        ?>
        <div class="smartshop-filters-sidebar">
            <h3 class="filters-title"><?php _e('Filter Products', 'smartshop'); ?></h3>
            
            <!-- Categories Filter -->
            <div class="filter-group">
                <h4 class="filter-title"><?php _e('Categories', 'smartshop'); ?></h4>
                <ul class="filter-list categories-filter">
                    <?php
                    $categories = SmartShop_Shortcodes::get_categories();
                    foreach ($categories as $category):
                        $count = SmartShop_Shortcodes::get_category_product_count($category->id);
                    ?>
                        <li class="filter-item">
                            <label class="filter-label">
                                <input type="checkbox" 
                                       name="categories[]" 
                                       value="<?php echo esc_attr($category->id); ?>"
                                       class="filter-checkbox">
                                <span class="filter-text">
                                    <?php echo esc_html($category->name); ?>
                                    <span class="filter-count">(<?php echo $count; ?>)</span>
                                </span>
                            </label>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
            
            <!-- Price Filter -->
            <div class="filter-group">
                <h4 class="filter-title"><?php _e('Price Range', 'smartshop'); ?></h4>
                <div class="price-filter">
                    <div class="price-inputs">
                        <input type="number" 
                               name="min_price" 
                               placeholder="<?php _e('Min', 'smartshop'); ?>"
                               class="price-input min-price"
                               value="<?php echo esc_attr($_GET['min_price'] ?? ''); ?>">
                        <span class="price-separator">-</span>
                        <input type="number" 
                               name="max_price" 
                               placeholder="<?php _e('Max', 'smartshop'); ?>"
                               class="price-input max-price"
                               value="<?php echo esc_attr($_GET['max_price'] ?? ''); ?>">
                    </div>
                    <button type="button" class="apply-price-filter btn btn-secondary">
                        <?php _e('Apply', 'smartshop'); ?>
                    </button>
                </div>
            </div>
            
            <!-- Stock Filter -->
            <div class="filter-group">
                <h4 class="filter-title"><?php _e('Availability', 'smartshop'); ?></h4>
                <ul class="filter-list stock-filter">
                    <li class="filter-item">
                        <label class="filter-label">
                            <input type="checkbox" 
                                   name="in_stock" 
                                   value="1"
                                   class="filter-checkbox">
                            <span class="filter-text"><?php _e('In Stock Only', 'smartshop'); ?></span>
                        </label>
                    </li>
                </ul>
            </div>
            
            <!-- Clear Filters -->
            <div class="filter-actions">
                <button type="button" class="clear-filters btn btn-outline">
                    <?php _e('Clear All Filters', 'smartshop'); ?>
                </button>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
}
