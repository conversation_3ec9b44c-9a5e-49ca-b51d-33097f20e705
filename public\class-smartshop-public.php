<?php
/**
 * SmartShop Public Class
 * 
 * Handles public-facing functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SmartShop_Public {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('template_redirect', array($this, 'template_redirect'));
        add_filter('query_vars', array($this, 'add_query_vars'));
        add_filter('template_include', array($this, 'template_include'));
        add_action('wp_head', array($this, 'add_custom_styles'));
        add_action('wp_footer', array($this, 'add_cart_modal'));
    }
    
    /**
     * Add custom query vars
     */
    public function add_query_vars($vars) {
        $vars[] = 'smartshop_page';
        $vars[] = 'product_slug';
        $vars[] = 'category_slug';
        return $vars;
    }
    
    /**
     * Handle template redirects
     */
    public function template_redirect() {
        $smartshop_page = get_query_var('smartshop_page');
        
        if ($smartshop_page) {
            $this->load_smartshop_template($smartshop_page);
        }
    }
    
    /**
     * Include custom templates
     */
    public function template_include($template) {
        $smartshop_page = get_query_var('smartshop_page');
        
        if ($smartshop_page) {
            $custom_template = $this->get_smartshop_template($smartshop_page);
            if ($custom_template) {
                return $custom_template;
            }
        }
        
        return $template;
    }
    
    /**
     * Load SmartShop template
     */
    private function load_smartshop_template($page) {
        switch ($page) {
            case 'shop':
                $this->load_shop_template();
                break;
            case 'product':
                $this->load_product_template();
                break;
            case 'category':
                $this->load_category_template();
                break;
            case 'cart':
                $this->load_cart_template();
                break;
            case 'checkout':
                $this->load_checkout_template();
                break;
            case 'account':
                $this->load_account_template();
                break;
        }
    }
    
    /**
     * Get SmartShop template path
     */
    private function get_smartshop_template($page) {
        $template_path = SMARTSHOP_PLUGIN_PATH . 'templates/';
        
        switch ($page) {
            case 'shop':
                return $template_path . 'shop.php';
            case 'product':
                return $template_path . 'single-product.php';
            case 'category':
                return $template_path . 'category.php';
            case 'cart':
                return $template_path . 'cart.php';
            case 'checkout':
                return $template_path . 'checkout.php';
            case 'account':
                return $template_path . 'account.php';
        }
        
        return false;
    }
    
    /**
     * Load shop template
     */
    private function load_shop_template() {
        global $wp_query;
        
        // Set page title
        add_filter('wp_title', function($title) {
            return SmartShop_Settings::get_option('shop_page_title', __('Shop', 'smartshop')) . ' | ' . get_bloginfo('name');
        });
        
        // Set page as main query
        $wp_query->is_home = false;
        $wp_query->is_page = true;
        $wp_query->is_singular = true;
    }
    
    /**
     * Load product template
     */
    private function load_product_template() {
        global $wp_query;
        
        $product_slug = get_query_var('product_slug');
        $product = SmartShop_Product::get_product_by_slug($product_slug);
        
        if (!$product) {
            $wp_query->set_404();
            status_header(404);
            return;
        }
        
        // Set global product
        global $smartshop_product;
        $smartshop_product = $product;
        
        // Set page title
        add_filter('wp_title', function($title) use ($product) {
            return $product->name . ' | ' . get_bloginfo('name');
        });
        
        $wp_query->is_home = false;
        $wp_query->is_page = true;
        $wp_query->is_singular = true;
    }
    
    /**
     * Load category template
     */
    private function load_category_template() {
        global $wp_query;
        
        $category_slug = get_query_var('category_slug');
        $category = $this->get_category_by_slug($category_slug);
        
        if (!$category) {
            $wp_query->set_404();
            status_header(404);
            return;
        }
        
        // Set global category
        global $smartshop_category;
        $smartshop_category = $category;
        
        // Set page title
        add_filter('wp_title', function($title) use ($category) {
            return $category->name . ' | ' . get_bloginfo('name');
        });
        
        $wp_query->is_home = false;
        $wp_query->is_page = true;
        $wp_query->is_archive = true;
    }
    
    /**
     * Load cart template
     */
    private function load_cart_template() {
        global $wp_query;
        
        add_filter('wp_title', function($title) {
            return __('Shopping Cart', 'smartshop') . ' | ' . get_bloginfo('name');
        });
        
        $wp_query->is_home = false;
        $wp_query->is_page = true;
        $wp_query->is_singular = true;
    }
    
    /**
     * Load checkout template
     */
    private function load_checkout_template() {
        global $wp_query;
        
        add_filter('wp_title', function($title) {
            return __('Checkout', 'smartshop') . ' | ' . get_bloginfo('name');
        });
        
        $wp_query->is_home = false;
        $wp_query->is_page = true;
        $wp_query->is_singular = true;
    }
    
    /**
     * Load account template
     */
    private function load_account_template() {
        global $wp_query;
        
        add_filter('wp_title', function($title) {
            return __('My Account', 'smartshop') . ' | ' . get_bloginfo('name');
        });
        
        $wp_query->is_home = false;
        $wp_query->is_page = true;
        $wp_query->is_singular = true;
    }
    
    /**
     * Get category by slug
     */
    private function get_category_by_slug($slug) {
        global $wpdb;
        
        $categories_table = SmartShop_Database::get_table_name('categories');
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $categories_table WHERE slug = %s AND status = 'active'",
            $slug
        ));
    }
    
    /**
     * Add custom styles to head
     */
    public function add_custom_styles() {
        $primary_color = SmartShop_Settings::get_option('primary_color', '#3b82f6');
        $secondary_color = SmartShop_Settings::get_option('secondary_color', '#1e40af');
        $accent_color = SmartShop_Settings::get_option('accent_color', '#f59e0b');
        
        echo '<style type="text/css">';
        echo ':root {';
        echo '--smartshop-primary-color: ' . esc_attr($primary_color) . ';';
        echo '--smartshop-secondary-color: ' . esc_attr($secondary_color) . ';';
        echo '--smartshop-accent-color: ' . esc_attr($accent_color) . ';';
        echo '}';
        echo '</style>';
    }
    
    /**
     * Add cart modal to footer
     */
    public function add_cart_modal() {
        if (SmartShop_Settings::get_option('enable_ajax_cart', 1)) {
            include SMARTSHOP_PLUGIN_PATH . 'templates/modals/cart-modal.php';
        }
    }
    
    /**
     * Get shop URL
     */
    public static function get_shop_url() {
        return home_url('/shop/');
    }
    
    /**
     * Get product URL
     */
    public static function get_product_url($product_slug) {
        return home_url('/shop/product/' . $product_slug . '/');
    }
    
    /**
     * Get category URL
     */
    public static function get_category_url($category_slug) {
        return home_url('/shop/category/' . $category_slug . '/');
    }
    
    /**
     * Get cart URL
     */
    public static function get_cart_url() {
        return home_url('/cart/');
    }
    
    /**
     * Get checkout URL
     */
    public static function get_checkout_url() {
        return home_url('/checkout/');
    }
    
    /**
     * Get account URL
     */
    public static function get_account_url() {
        return home_url('/my-account/');
    }
    
    /**
     * Check if current page is SmartShop page
     */
    public static function is_smartshop_page() {
        return get_query_var('smartshop_page') !== '';
    }
    
    /**
     * Check if current page is shop page
     */
    public static function is_shop_page() {
        return get_query_var('smartshop_page') === 'shop';
    }
    
    /**
     * Check if current page is product page
     */
    public static function is_product_page() {
        return get_query_var('smartshop_page') === 'product';
    }
    
    /**
     * Check if current page is category page
     */
    public static function is_category_page() {
        return get_query_var('smartshop_page') === 'category';
    }
    
    /**
     * Check if current page is cart page
     */
    public static function is_cart_page() {
        return get_query_var('smartshop_page') === 'cart';
    }
    
    /**
     * Check if current page is checkout page
     */
    public static function is_checkout_page() {
        return get_query_var('smartshop_page') === 'checkout';
    }
    
    /**
     * Check if current page is account page
     */
    public static function is_account_page() {
        return get_query_var('smartshop_page') === 'account';
    }
}
