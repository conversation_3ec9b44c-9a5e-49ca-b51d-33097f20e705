<?php
/**
 * Plugin Name: SmartShop WebApp Plugin
 * Plugin URI: https://smartshop.com
 * Description: A modern, multipurpose WordPress plugin that transforms any WordPress site into a dynamic eCommerce web app without requiring WooCommerce. Perfect for grocery, fashion, electronics, beauty, and other product niches.
 * Version: 1.0.0
 * Author: SmartShop Team
 * Author URI: https://smartshop.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: smartshop
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check PHP version
if (version_compare(PHP_VERSION, '7.4', '<')) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error"><p>';
        echo '<strong>SmartShop Plugin:</strong> This plugin requires PHP 7.4 or higher. ';
        echo 'You are running PHP ' . PHP_VERSION . '. Please upgrade P<PERSON> to activate this plugin.';
        echo '</p></div>';
    });
    return;
}

// Define plugin constants
if (!defined('SMARTSHOP_VERSION')) {
    define('SMARTSHOP_VERSION', '1.0.0');
}
if (!defined('SMARTSHOP_PLUGIN_FILE')) {
    define('SMARTSHOP_PLUGIN_FILE', __FILE__);
}
if (!defined('SMARTSHOP_PLUGIN_PATH')) {
    define('SMARTSHOP_PLUGIN_PATH', plugin_dir_path(__FILE__));
}
if (!defined('SMARTSHOP_PLUGIN_URL')) {
    define('SMARTSHOP_PLUGIN_URL', plugin_dir_url(__FILE__));
}
if (!defined('SMARTSHOP_PLUGIN_BASENAME')) {
    define('SMARTSHOP_PLUGIN_BASENAME', plugin_basename(__FILE__));
}

/**
 * Main SmartShop Plugin Class
 */
final class SmartShop_Plugin {

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->define_constants();
        $this->init_hooks();
        $this->include_files();
        $this->init_classes();
    }

    /**
     * Define additional constants
     */
    private function define_constants() {
        define('SMARTSHOP_ASSETS_URL', SMARTSHOP_PLUGIN_URL . 'assets/');
        define('SMARTSHOP_TEMPLATES_PATH', SMARTSHOP_PLUGIN_PATH . 'templates/');
        define('SMARTSHOP_INCLUDES_PATH', SMARTSHOP_PLUGIN_PATH . 'includes/');
        define('SMARTSHOP_ADMIN_PATH', SMARTSHOP_PLUGIN_PATH . 'admin/');
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));

        add_action('plugins_loaded', array($this, 'load_textdomain'));
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_smartshop_add_to_cart', array($this, 'ajax_add_to_cart'));
        add_action('wp_ajax_nopriv_smartshop_add_to_cart', array($this, 'ajax_add_to_cart'));
        add_action('wp_ajax_smartshop_update_cart', array($this, 'ajax_update_cart'));
        add_action('wp_ajax_nopriv_smartshop_update_cart', array($this, 'ajax_update_cart'));
        add_action('wp_ajax_smartshop_remove_from_cart', array($this, 'ajax_remove_from_cart'));
        add_action('wp_ajax_nopriv_smartshop_remove_from_cart', array($this, 'ajax_remove_from_cart'));
        add_action('wp_ajax_smartshop_get_cart', array($this, 'ajax_get_cart'));
        add_action('wp_ajax_nopriv_smartshop_get_cart', array($this, 'ajax_get_cart'));
    }

    /**
     * Include required files
     */
    private function include_files() {
        // Core includes
        require_once SMARTSHOP_INCLUDES_PATH . 'class-smartshop-database.php';
        require_once SMARTSHOP_INCLUDES_PATH . 'class-smartshop-settings.php';
        require_once SMARTSHOP_INCLUDES_PATH . 'class-smartshop-product.php';
        require_once SMARTSHOP_INCLUDES_PATH . 'class-smartshop-category.php';
        require_once SMARTSHOP_INCLUDES_PATH . 'class-smartshop-cart.php';
        require_once SMARTSHOP_INCLUDES_PATH . 'class-smartshop-order.php';
        require_once SMARTSHOP_INCLUDES_PATH . 'class-smartshop-shortcodes.php';
        require_once SMARTSHOP_INCLUDES_PATH . 'class-smartshop-email.php';
        require_once SMARTSHOP_INCLUDES_PATH . 'class-smartshop-ajax.php';
        require_once SMARTSHOP_INCLUDES_PATH . 'class-smartshop-payment.php';
        require_once SMARTSHOP_INCLUDES_PATH . 'class-smartshop-import-export.php';

        // Admin includes
        if (is_admin()) {
            require_once SMARTSHOP_ADMIN_PATH . 'class-smartshop-admin.php';
        }

        // Public includes
        require_once SMARTSHOP_PLUGIN_PATH . 'public/class-smartshop-public.php';
    }

    /**
     * Initialize classes
     */
    private function init_classes() {
        // Initialize core classes
        SmartShop_Database::get_instance();
        SmartShop_Settings::get_instance();
        SmartShop_Product::get_instance();
        SmartShop_Category::get_instance();
        SmartShop_Cart::get_instance();
        SmartShop_Order::get_instance();
        SmartShop_Shortcodes::get_instance();
        SmartShop_Email::get_instance();
        SmartShop_Ajax::get_instance();
        SmartShop_Payment::get_instance();
        SmartShop_Import_Export::get_instance();

        // Initialize admin
        if (is_admin()) {
            SmartShop_Admin::get_instance();
        }

        // Initialize public
        SmartShop_Public::get_instance();
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        SmartShop_Database::create_tables();

        // Set default options
        SmartShop_Settings::set_default_options();

        // Create default categories
        $this->create_default_categories();

        // Set activation flag
        update_option('smartshop_activated', true);
        update_option('smartshop_version', SMARTSHOP_VERSION);

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled events
        wp_clear_scheduled_hook('smartshop_cleanup');

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Load text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain('smartshop', false, dirname(SMARTSHOP_PLUGIN_BASENAME) . '/languages');
    }

    /**
     * Initialize plugin
     */
    public function init() {
        // Add rewrite rules for virtual pages
        $this->add_rewrite_rules();

        // Initialize session if not started
        if (!session_id() && !headers_sent()) {
            session_start();
        }
    }

    /**
     * Add rewrite rules
     */
    private function add_rewrite_rules() {
        add_rewrite_rule('^shop/?$', 'index.php?smartshop_page=shop', 'top');
        add_rewrite_rule('^shop/page/([0-9]+)/?$', 'index.php?smartshop_page=shop&paged=$matches[1]', 'top');
        add_rewrite_rule('^shop/category/([^/]+)/?$', 'index.php?smartshop_page=shop&smartshop_category=$matches[1]', 'top');
        add_rewrite_rule('^product/([^/]+)/?$', 'index.php?smartshop_page=product&smartshop_product=$matches[1]', 'top');
        add_rewrite_rule('^cart/?$', 'index.php?smartshop_page=cart', 'top');
        add_rewrite_rule('^checkout/?$', 'index.php?smartshop_page=checkout', 'top');
        add_rewrite_rule('^my-account/?$', 'index.php?smartshop_page=account', 'top');

        // Add query vars
        add_filter('query_vars', function($vars) {
            $vars[] = 'smartshop_page';
            $vars[] = 'smartshop_category';
            $vars[] = 'smartshop_product';
            return $vars;
        });

        // Template redirect
        add_action('template_redirect', array($this, 'template_redirect'));
    }

    /**
     * Template redirect
     */
    public function template_redirect() {
        $page = get_query_var('smartshop_page');

        if ($page) {
            $template_file = SMARTSHOP_TEMPLATES_PATH . $page . '.php';

            if (file_exists($template_file)) {
                include $template_file;
                exit;
            }
        }
    }

    /**
     * Enqueue frontend scripts
     */
    public function enqueue_frontend_scripts() {
        wp_enqueue_style('smartshop-frontend', SMARTSHOP_ASSETS_URL . 'css/frontend.css', array(), SMARTSHOP_VERSION);
        wp_enqueue_script('smartshop-frontend', SMARTSHOP_ASSETS_URL . 'js/frontend.js', array('jquery'), SMARTSHOP_VERSION, true);

        // Localize script
        wp_localize_script('smartshop-frontend', 'smartshop_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('smartshop_nonce'),
            'currency_symbol' => SmartShop_Settings::get_option('currency_symbol', '$'),
            'currency_position' => SmartShop_Settings::get_option('currency_position', 'left'),
            'cart_url' => home_url('/cart/'),
            'checkout_url' => home_url('/checkout/'),
        ));
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'smartshop') !== false) {
            wp_enqueue_style('smartshop-admin', SMARTSHOP_ASSETS_URL . 'css/admin.css', array(), SMARTSHOP_VERSION);
            wp_enqueue_script('smartshop-admin', SMARTSHOP_ASSETS_URL . 'js/admin.js', array('jquery'), SMARTSHOP_VERSION, true);

            // Enqueue media uploader
            wp_enqueue_media();

            // Color picker
            wp_enqueue_style('wp-color-picker');
            wp_enqueue_script('wp-color-picker');
        }
    }

    /**
     * Create default categories
     */
    private function create_default_categories() {
        $default_categories = array(
            array('name' => 'Electronics', 'slug' => 'electronics', 'description' => 'Electronic devices and gadgets'),
            array('name' => 'Fashion', 'slug' => 'fashion', 'description' => 'Clothing and accessories'),
            array('name' => 'Home & Garden', 'slug' => 'home-garden', 'description' => 'Home and garden products'),
            array('name' => 'Beauty', 'slug' => 'beauty', 'description' => 'Beauty and personal care'),
            array('name' => 'Sports', 'slug' => 'sports', 'description' => 'Sports and outdoor equipment'),
        );

        foreach ($default_categories as $category) {
            SmartShop_Category::create_category($category);
        }
    }

    /**
     * AJAX handlers
     */
    public function ajax_add_to_cart() {
        SmartShop_Ajax::add_to_cart();
    }

    public function ajax_update_cart() {
        SmartShop_Ajax::update_cart();
    }

    public function ajax_remove_from_cart() {
        SmartShop_Ajax::remove_from_cart();
    }

    public function ajax_get_cart() {
        SmartShop_Ajax::get_cart();
    }
}

// Initialize the plugin
SmartShop_Plugin::get_instance();
