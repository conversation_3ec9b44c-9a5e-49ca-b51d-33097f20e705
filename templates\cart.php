<?php
/**
 * Cart Template
 * 
 * Displays the shopping cart page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header();

$cart = SmartShop_Cart::get_instance();
$cart_items = $cart->get_cart_items();
$cart_total = $cart->get_cart_total();
$is_empty = $cart->is_empty();
?>

<div class="smartshop-container">
    <div class="smartshop-cart-wrapper">
        
        <!-- Cart Header -->
        <div class="smartshop-cart-header">
            <div class="smartshop-breadcrumbs">
                <a href="<?php echo home_url(); ?>"><?php _e('Home', 'smartshop'); ?></a>
                <span class="separator">/</span>
                <a href="<?php echo SmartShop_Public::get_shop_url(); ?>"><?php _e('Shop', 'smartshop'); ?></a>
                <span class="separator">/</span>
                <span class="current"><?php _e('Shopping Cart', 'smartshop'); ?></span>
            </div>
            
            <h1 class="smartshop-page-title"><?php _e('Shopping Cart', 'smartshop'); ?></h1>
        </div>

        <div class="smartshop-cart-content">
            
            <?php if (!$is_empty): ?>
            
            <!-- Cart Table -->
            <div class="smartshop-cart-table-wrapper">
                <table class="smartshop-cart-table">
                    <thead>
                        <tr>
                            <th class="product-thumbnail"><?php _e('Product', 'smartshop'); ?></th>
                            <th class="product-name"></th>
                            <th class="product-price"><?php _e('Price', 'smartshop'); ?></th>
                            <th class="product-quantity"><?php _e('Quantity', 'smartshop'); ?></th>
                            <th class="product-subtotal"><?php _e('Subtotal', 'smartshop'); ?></th>
                            <th class="product-remove"></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($cart_items as $item): ?>
                        <tr class="cart-item" data-product-id="<?php echo $item['product_id']; ?>">
                            
                            <!-- Product Image -->
                            <td class="product-thumbnail">
                                <a href="<?php echo SmartShop_Public::get_product_url($item['product']->slug); ?>">
                                    <?php if ($item['product']->image_url): ?>
                                        <img src="<?php echo esc_url($item['product']->image_url); ?>" alt="<?php echo esc_attr($item['product']->name); ?>" width="80" height="80">
                                    <?php else: ?>
                                        <div class="no-image">
                                            <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                                <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                                <polyline points="21,15 16,10 5,21"></polyline>
                                            </svg>
                                        </div>
                                    <?php endif; ?>
                                </a>
                            </td>
                            
                            <!-- Product Name -->
                            <td class="product-name">
                                <a href="<?php echo SmartShop_Public::get_product_url($item['product']->slug); ?>">
                                    <?php echo esc_html($item['product']->name); ?>
                                </a>
                                <?php if ($item['product']->sku): ?>
                                    <div class="product-sku">
                                        <small><?php printf(__('SKU: %s', 'smartshop'), $item['product']->sku); ?></small>
                                    </div>
                                <?php endif; ?>
                            </td>
                            
                            <!-- Product Price -->
                            <td class="product-price">
                                <span class="amount"><?php echo SmartShop_Settings::format_currency($item['price']); ?></span>
                            </td>
                            
                            <!-- Quantity -->
                            <td class="product-quantity">
                                <div class="quantity-input">
                                    <button type="button" class="quantity-minus" data-product-id="<?php echo $item['product_id']; ?>">-</button>
                                    <input type="number" class="quantity-field" value="<?php echo $item['quantity']; ?>" min="1" max="<?php echo $item['product']->stock_quantity ?: 999; ?>" data-product-id="<?php echo $item['product_id']; ?>">
                                    <button type="button" class="quantity-plus" data-product-id="<?php echo $item['product_id']; ?>">+</button>
                                </div>
                            </td>
                            
                            <!-- Subtotal -->
                            <td class="product-subtotal">
                                <span class="amount"><?php echo SmartShop_Settings::format_currency($item['total']); ?></span>
                            </td>
                            
                            <!-- Remove -->
                            <td class="product-remove">
                                <button type="button" class="remove-item" data-product-id="<?php echo $item['product_id']; ?>" title="<?php _e('Remove this item', 'smartshop'); ?>">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <polyline points="3,6 5,6 21,6"></polyline>
                                        <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"></path>
                                        <line x1="10" y1="11" x2="10" y2="17"></line>
                                        <line x1="14" y1="11" x2="14" y2="17"></line>
                                    </svg>
                                </button>
                            </td>
                            
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Cart Actions -->
            <div class="smartshop-cart-actions">
                <div class="cart-actions-left">
                    <a href="<?php echo SmartShop_Public::get_shop_url(); ?>" class="btn btn-secondary">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="15,18 9,12 15,6"></polyline>
                        </svg>
                        <?php _e('Continue Shopping', 'smartshop'); ?>
                    </a>
                </div>
                
                <div class="cart-actions-right">
                    <button type="button" class="btn btn-outline clear-cart-btn">
                        <?php _e('Clear Cart', 'smartshop'); ?>
                    </button>
                    <button type="button" class="btn btn-primary update-cart-btn">
                        <?php _e('Update Cart', 'smartshop'); ?>
                    </button>
                </div>
            </div>

            <!-- Cart Totals -->
            <div class="smartshop-cart-totals">
                <h3><?php _e('Cart Totals', 'smartshop'); ?></h3>
                
                <table class="cart-totals-table">
                    <tbody>
                        <tr class="cart-subtotal">
                            <th><?php _e('Subtotal', 'smartshop'); ?></th>
                            <td><span class="amount"><?php echo SmartShop_Settings::format_currency($cart_total); ?></span></td>
                        </tr>
                        
                        <?php if (SmartShop_Settings::get_option('enable_shipping', false)): ?>
                        <tr class="shipping">
                            <th><?php _e('Shipping', 'smartshop'); ?></th>
                            <td>
                                <?php 
                                $shipping_cost = SmartShop_Settings::get_option('shipping_cost', 0);
                                if ($shipping_cost > 0): ?>
                                    <span class="amount"><?php echo SmartShop_Settings::format_currency($shipping_cost); ?></span>
                                <?php else: ?>
                                    <?php _e('Free shipping', 'smartshop'); ?>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endif; ?>
                        
                        <?php if (SmartShop_Settings::get_option('enable_tax', false)): ?>
                        <tr class="tax">
                            <th><?php _e('Tax', 'smartshop'); ?></th>
                            <td>
                                <?php 
                                $tax_rate = SmartShop_Settings::get_option('tax_rate', 0);
                                $tax_amount = ($cart_total * $tax_rate) / 100;
                                ?>
                                <span class="amount"><?php echo SmartShop_Settings::format_currency($tax_amount); ?></span>
                            </td>
                        </tr>
                        <?php endif; ?>
                        
                        <tr class="order-total">
                            <th><?php _e('Total', 'smartshop'); ?></th>
                            <td>
                                <strong>
                                    <span class="amount">
                                        <?php 
                                        $total = $cart_total;
                                        if (SmartShop_Settings::get_option('enable_shipping', false)) {
                                            $total += SmartShop_Settings::get_option('shipping_cost', 0);
                                        }
                                        if (SmartShop_Settings::get_option('enable_tax', false)) {
                                            $tax_rate = SmartShop_Settings::get_option('tax_rate', 0);
                                            $total += ($cart_total * $tax_rate) / 100;
                                        }
                                        echo SmartShop_Settings::format_currency($total);
                                        ?>
                                    </span>
                                </strong>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="proceed-to-checkout">
                    <a href="<?php echo SmartShop_Public::get_checkout_url(); ?>" class="btn btn-primary btn-large">
                        <?php _e('Proceed to Checkout', 'smartshop'); ?>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="9,18 15,12 9,6"></polyline>
                        </svg>
                    </a>
                </div>
            </div>

            <?php else: ?>
            
            <!-- Empty Cart -->
            <div class="smartshop-empty-cart">
                <div class="empty-cart-icon">
                    <svg width="100" height="100" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                        <circle cx="9" cy="21" r="1"></circle>
                        <circle cx="20" cy="21" r="1"></circle>
                        <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                    </svg>
                </div>
                
                <h3><?php _e('Your cart is currently empty', 'smartshop'); ?></h3>
                <p><?php _e('Looks like you haven\'t added any items to your cart yet. Start shopping to fill it up!', 'smartshop'); ?></p>
                
                <div class="empty-cart-actions">
                    <a href="<?php echo SmartShop_Public::get_shop_url(); ?>" class="btn btn-primary btn-large">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="9" cy="21" r="1"></circle>
                            <circle cx="20" cy="21" r="1"></circle>
                            <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                        </svg>
                        <?php _e('Start Shopping', 'smartshop'); ?>
                    </a>
                </div>
            </div>
            
            <?php endif; ?>
            
        </div>
        
    </div>
</div>

<!-- Loading Overlay -->
<div class="smartshop-loading-overlay" style="display: none;">
    <div class="loading-spinner">
        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 12a9 9 0 11-6.219-8.56"/>
        </svg>
    </div>
</div>

<?php get_footer(); ?>
