<?php
/**
 * Checkout Template
 * 
 * Displays the checkout page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header();

$cart = SmartShop_Cart::get_instance();
$cart_items = $cart->get_cart_items();
$cart_total = $cart->get_cart_total();
$is_empty = $cart->is_empty();

// Redirect to cart if empty
if ($is_empty) {
    wp_redirect(SmartShop_Public::get_cart_url());
    exit;
}

// Calculate totals
$subtotal = $cart_total;
$shipping_cost = SmartShop_Settings::get_option('enable_shipping', false) ? SmartShop_Settings::get_option('shipping_cost', 0) : 0;
$tax_rate = SmartShop_Settings::get_option('enable_tax', false) ? SmartShop_Settings::get_option('tax_rate', 0) : 0;
$tax_amount = ($subtotal * $tax_rate) / 100;
$total = $subtotal + $shipping_cost + $tax_amount;
?>

<div class="smartshop-container">
    <div class="smartshop-checkout-wrapper">
        
        <!-- Checkout Header -->
        <div class="smartshop-checkout-header">
            <div class="smartshop-breadcrumbs">
                <a href="<?php echo home_url(); ?>"><?php _e('Home', 'smartshop'); ?></a>
                <span class="separator">/</span>
                <a href="<?php echo SmartShop_Public::get_shop_url(); ?>"><?php _e('Shop', 'smartshop'); ?></a>
                <span class="separator">/</span>
                <a href="<?php echo SmartShop_Public::get_cart_url(); ?>"><?php _e('Cart', 'smartshop'); ?></a>
                <span class="separator">/</span>
                <span class="current"><?php _e('Checkout', 'smartshop'); ?></span>
            </div>
            
            <h1 class="smartshop-page-title"><?php _e('Checkout', 'smartshop'); ?></h1>
        </div>

        <div class="smartshop-checkout-content">
            
            <form id="smartshop-checkout-form" class="checkout-form">
                
                <div class="checkout-columns">
                    
                    <!-- Billing Details -->
                    <div class="checkout-billing">
                        <h3><?php _e('Billing Details', 'smartshop'); ?></h3>
                        
                        <div class="form-row form-row-wide">
                            <div class="form-group">
                                <label for="first_name"><?php _e('First Name', 'smartshop'); ?> <span class="required">*</span></label>
                                <input type="text" id="first_name" name="first_name" required>
                            </div>
                            <div class="form-group">
                                <label for="last_name"><?php _e('Last Name', 'smartshop'); ?> <span class="required">*</span></label>
                                <input type="text" id="last_name" name="last_name" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="email"><?php _e('Email Address', 'smartshop'); ?> <span class="required">*</span></label>
                                <input type="email" id="email" name="email" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="phone"><?php _e('Phone Number', 'smartshop'); ?> <span class="required">*</span></label>
                                <input type="tel" id="phone" name="phone" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="address"><?php _e('Street Address', 'smartshop'); ?> <span class="required">*</span></label>
                                <input type="text" id="address" name="address" placeholder="<?php _e('House number and street name', 'smartshop'); ?>" required>
                            </div>
                        </div>
                        
                        <div class="form-row form-row-wide">
                            <div class="form-group">
                                <label for="city"><?php _e('Town / City', 'smartshop'); ?> <span class="required">*</span></label>
                                <input type="text" id="city" name="city" required>
                            </div>
                            <div class="form-group">
                                <label for="state"><?php _e('State / County', 'smartshop'); ?></label>
                                <input type="text" id="state" name="state">
                            </div>
                        </div>
                        
                        <div class="form-row form-row-wide">
                            <div class="form-group">
                                <label for="postcode"><?php _e('Postcode / ZIP', 'smartshop'); ?></label>
                                <input type="text" id="postcode" name="postcode">
                            </div>
                            <div class="form-group">
                                <label for="country"><?php _e('Country', 'smartshop'); ?></label>
                                <select id="country" name="country">
                                    <option value=""><?php _e('Select Country', 'smartshop'); ?></option>
                                    <option value="US"><?php _e('United States', 'smartshop'); ?></option>
                                    <option value="CA"><?php _e('Canada', 'smartshop'); ?></option>
                                    <option value="GB"><?php _e('United Kingdom', 'smartshop'); ?></option>
                                    <option value="AU"><?php _e('Australia', 'smartshop'); ?></option>
                                    <option value="DE"><?php _e('Germany', 'smartshop'); ?></option>
                                    <option value="FR"><?php _e('France', 'smartshop'); ?></option>
                                    <option value="IT"><?php _e('Italy', 'smartshop'); ?></option>
                                    <option value="ES"><?php _e('Spain', 'smartshop'); ?></option>
                                    <option value="NL"><?php _e('Netherlands', 'smartshop'); ?></option>
                                    <option value="BE"><?php _e('Belgium', 'smartshop'); ?></option>
                                    <option value="CH"><?php _e('Switzerland', 'smartshop'); ?></option>
                                    <option value="AT"><?php _e('Austria', 'smartshop'); ?></option>
                                    <option value="SE"><?php _e('Sweden', 'smartshop'); ?></option>
                                    <option value="NO"><?php _e('Norway', 'smartshop'); ?></option>
                                    <option value="DK"><?php _e('Denmark', 'smartshop'); ?></option>
                                    <option value="FI"><?php _e('Finland', 'smartshop'); ?></option>
                                    <option value="JP"><?php _e('Japan', 'smartshop'); ?></option>
                                    <option value="KR"><?php _e('South Korea', 'smartshop'); ?></option>
                                    <option value="CN"><?php _e('China', 'smartshop'); ?></option>
                                    <option value="IN"><?php _e('India', 'smartshop'); ?></option>
                                    <option value="BR"><?php _e('Brazil', 'smartshop'); ?></option>
                                    <option value="MX"><?php _e('Mexico', 'smartshop'); ?></option>
                                    <option value="AR"><?php _e('Argentina', 'smartshop'); ?></option>
                                    <option value="ZA"><?php _e('South Africa', 'smartshop'); ?></option>
                                    <option value="EG"><?php _e('Egypt', 'smartshop'); ?></option>
                                    <option value="NG"><?php _e('Nigeria', 'smartshop'); ?></option>
                                    <option value="KE"><?php _e('Kenya', 'smartshop'); ?></option>
                                    <option value="other"><?php _e('Other', 'smartshop'); ?></option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="order_notes"><?php _e('Order Notes', 'smartshop'); ?></label>
                                <textarea id="order_notes" name="order_notes" rows="4" placeholder="<?php _e('Notes about your order, e.g. special notes for delivery.', 'smartshop'); ?>"></textarea>
                            </div>
                        </div>
                        
                        <!-- Payment Methods -->
                        <div class="payment-methods">
                            <h3><?php _e('Payment Method', 'smartshop'); ?></h3>
                            
                            <?php if (SmartShop_Settings::get_option('enable_cod', true)): ?>
                            <div class="payment-method">
                                <input type="radio" id="payment_cod" name="payment_method" value="cod" checked>
                                <label for="payment_cod">
                                    <span class="payment-title"><?php _e('Cash on Delivery', 'smartshop'); ?></span>
                                    <span class="payment-description"><?php echo SmartShop_Settings::get_option('cod_instructions', __('Pay with cash upon delivery.', 'smartshop')); ?></span>
                                </label>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (SmartShop_Settings::get_option('enable_manual', true)): ?>
                            <div class="payment-method">
                                <input type="radio" id="payment_manual" name="payment_method" value="manual">
                                <label for="payment_manual">
                                    <span class="payment-title"><?php _e('Manual Payment', 'smartshop'); ?></span>
                                    <span class="payment-description"><?php echo SmartShop_Settings::get_option('manual_instructions', __('Please transfer the amount to our bank account.', 'smartshop')); ?></span>
                                </label>
                            </div>
                            <?php endif; ?>
                            
                            <?php do_action('smartshop_checkout_payment_methods'); ?>
                        </div>
                        
                    </div>
                    
                    <!-- Order Summary -->
                    <div class="checkout-summary">
                        <h3><?php _e('Your Order', 'smartshop'); ?></h3>
                        
                        <div class="order-review">
                            <table class="order-review-table">
                                <thead>
                                    <tr>
                                        <th class="product-name"><?php _e('Product', 'smartshop'); ?></th>
                                        <th class="product-total"><?php _e('Total', 'smartshop'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($cart_items as $item): ?>
                                    <tr class="cart-item">
                                        <td class="product-name">
                                            <?php echo esc_html($item['product']->name); ?>
                                            <strong class="product-quantity"> × <?php echo $item['quantity']; ?></strong>
                                        </td>
                                        <td class="product-total">
                                            <span class="amount"><?php echo SmartShop_Settings::format_currency($item['total']); ?></span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr class="cart-subtotal">
                                        <th><?php _e('Subtotal', 'smartshop'); ?></th>
                                        <td><span class="amount"><?php echo SmartShop_Settings::format_currency($subtotal); ?></span></td>
                                    </tr>
                                    
                                    <?php if ($shipping_cost > 0): ?>
                                    <tr class="shipping">
                                        <th><?php _e('Shipping', 'smartshop'); ?></th>
                                        <td><span class="amount"><?php echo SmartShop_Settings::format_currency($shipping_cost); ?></span></td>
                                    </tr>
                                    <?php endif; ?>
                                    
                                    <?php if ($tax_amount > 0): ?>
                                    <tr class="tax">
                                        <th><?php _e('Tax', 'smartshop'); ?></th>
                                        <td><span class="amount"><?php echo SmartShop_Settings::format_currency($tax_amount); ?></span></td>
                                    </tr>
                                    <?php endif; ?>
                                    
                                    <tr class="order-total">
                                        <th><?php _e('Total', 'smartshop'); ?></th>
                                        <td><strong><span class="amount"><?php echo SmartShop_Settings::format_currency($total); ?></span></strong></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        
                        <!-- Terms and Conditions -->
                        <?php if (SmartShop_Settings::get_option('terms_page_id')): ?>
                        <div class="terms-conditions">
                            <label class="checkbox-label">
                                <input type="checkbox" id="terms_agreement" name="terms_agreement" required>
                                <span class="checkmark"></span>
                                <?php printf(
                                    __('I have read and agree to the website %s', 'smartshop'),
                                    '<a href="' . get_permalink(SmartShop_Settings::get_option('terms_page_id')) . '" target="_blank">' . __('terms and conditions', 'smartshop') . '</a>'
                                ); ?>
                            </label>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Privacy Policy -->
                        <?php if (SmartShop_Settings::get_option('privacy_page_id')): ?>
                        <div class="privacy-policy">
                            <label class="checkbox-label">
                                <input type="checkbox" id="privacy_agreement" name="privacy_agreement" required>
                                <span class="checkmark"></span>
                                <?php printf(
                                    __('I agree to the %s', 'smartshop'),
                                    '<a href="' . get_permalink(SmartShop_Settings::get_option('privacy_page_id')) . '" target="_blank">' . __('privacy policy', 'smartshop') . '</a>'
                                ); ?>
                            </label>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Place Order Button -->
                        <div class="place-order">
                            <button type="submit" class="btn btn-primary btn-large place-order-btn">
                                <?php _e('Place Order', 'smartshop'); ?>
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="20,6 9,17 4,12"></polyline>
                                </svg>
                            </button>
                        </div>
                        
                    </div>
                    
                </div>
                
                <?php wp_nonce_field('smartshop_checkout', 'smartshop_checkout_nonce'); ?>
                
            </form>
            
        </div>
        
    </div>
</div>

<!-- Loading Overlay -->
<div class="smartshop-loading-overlay" style="display: none;">
    <div class="loading-spinner">
        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 12a9 9 0 11-6.219-8.56"/>
        </svg>
    </div>
</div>

<?php get_footer(); ?>
