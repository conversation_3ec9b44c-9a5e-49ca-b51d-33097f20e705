<?php
/**
 * Order Confirmation Email Template
 * 
 * This template is used for order confirmation emails sent to customers
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$shop_name = SmartShop_Settings::get_option('shop_name', get_bloginfo('name'));
$shop_url = SmartShop_Public::get_shop_url();
$primary_color = SmartShop_Settings::get_option('primary_color', '#3b82f6');
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php printf(__('Order Confirmation - %s', 'smartshop'), $order->order_number); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .email-header {
            background-color: <?php echo esc_attr($primary_color); ?>;
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .email-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: normal;
        }
        .email-body {
            padding: 30px 20px;
        }
        .order-info {
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .order-info h2 {
            margin: 0 0 15px 0;
            font-size: 18px;
            color: <?php echo esc_attr($primary_color); ?>;
        }
        .order-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .order-details strong {
            color: #333;
        }
        .order-items {
            margin: 30px 0;
        }
        .order-items h3 {
            margin: 0 0 20px 0;
            font-size: 18px;
            color: #333;
            border-bottom: 2px solid <?php echo esc_attr($primary_color); ?>;
            padding-bottom: 10px;
        }
        .item-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .item-table th {
            background-color: #f9f9f9;
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            font-weight: 600;
        }
        .item-table td {
            padding: 12px;
            border-bottom: 1px solid #eee;
        }
        .item-table tr:last-child td {
            border-bottom: none;
        }
        .order-total {
            background-color: <?php echo esc_attr($primary_color); ?>;
            color: white;
            padding: 15px 20px;
            border-radius: 6px;
            text-align: right;
            margin: 20px 0;
        }
        .order-total h3 {
            margin: 0;
            font-size: 20px;
        }
        .billing-info {
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .billing-info h3 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: <?php echo esc_attr($primary_color); ?>;
        }
        .billing-address {
            line-height: 1.8;
        }
        .payment-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .payment-info h4 {
            margin: 0 0 10px 0;
            color: #856404;
        }
        .email-footer {
            background-color: #f9f9f9;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #eee;
        }
        .email-footer p {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }
        .button {
            display: inline-block;
            background-color: <?php echo esc_attr($primary_color); ?>;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 15px 0;
        }
        .button:hover {
            opacity: 0.9;
        }
        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            .email-body {
                padding: 20px 15px;
            }
            .order-details {
                flex-direction: column;
            }
            .item-table {
                font-size: 14px;
            }
            .item-table th,
            .item-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        
        <!-- Email Header -->
        <div class="email-header">
            <h1><?php _e('Order Confirmation', 'smartshop'); ?></h1>
            <p><?php printf(__('Thank you for your order from %s!', 'smartshop'), esc_html($shop_name)); ?></p>
        </div>
        
        <!-- Email Body -->
        <div class="email-body">
            
            <!-- Greeting -->
            <p><?php printf(__('Hi %s,', 'smartshop'), esc_html($order->billing_data['first_name'])); ?></p>
            
            <p><?php _e('We have received your order and it is now being processed. Your order details are shown below for your reference:', 'smartshop'); ?></p>
            
            <!-- Order Information -->
            <div class="order-info">
                <h2><?php _e('Order Information', 'smartshop'); ?></h2>
                
                <div class="order-details">
                    <span><strong><?php _e('Order Number:', 'smartshop'); ?></strong></span>
                    <span><?php echo esc_html($order->order_number); ?></span>
                </div>
                
                <div class="order-details">
                    <span><strong><?php _e('Order Date:', 'smartshop'); ?></strong></span>
                    <span><?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($order->created_at)); ?></span>
                </div>
                
                <div class="order-details">
                    <span><strong><?php _e('Payment Method:', 'smartshop'); ?></strong></span>
                    <span><?php echo esc_html(ucfirst(str_replace('_', ' ', $order->payment_method))); ?></span>
                </div>
                
                <div class="order-details">
                    <span><strong><?php _e('Order Status:', 'smartshop'); ?></strong></span>
                    <span><?php echo esc_html(ucfirst($order->status)); ?></span>
                </div>
            </div>
            
            <!-- Order Items -->
            <div class="order-items">
                <h3><?php _e('Order Items', 'smartshop'); ?></h3>
                
                <table class="item-table">
                    <thead>
                        <tr>
                            <th><?php _e('Product', 'smartshop'); ?></th>
                            <th><?php _e('Quantity', 'smartshop'); ?></th>
                            <th><?php _e('Price', 'smartshop'); ?></th>
                            <th><?php _e('Total', 'smartshop'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($order->items as $item): ?>
                            <tr>
                                <td>
                                    <strong><?php echo esc_html($item->product_name); ?></strong>
                                    <?php if ($item->product_sku): ?>
                                        <br><small><?php printf(__('SKU: %s', 'smartshop'), esc_html($item->product_sku)); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo esc_html($item->quantity); ?></td>
                                <td><?php echo SmartShop_Settings::format_currency($item->price); ?></td>
                                <td><?php echo SmartShop_Settings::format_currency($item->total); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Order Total -->
            <div class="order-total">
                <h3><?php printf(__('Total: %s', 'smartshop'), SmartShop_Settings::format_currency($order->total)); ?></h3>
            </div>
            
            <!-- Billing Information -->
            <div class="billing-info">
                <h3><?php _e('Billing Information', 'smartshop'); ?></h3>
                <div class="billing-address">
                    <?php echo esc_html($order->billing_data['first_name'] . ' ' . $order->billing_data['last_name']); ?><br>
                    <?php echo esc_html($order->billing_data['address']); ?><br>
                    <?php echo esc_html($order->billing_data['city']); ?>
                    <?php if ($order->billing_data['state']): ?>
                        , <?php echo esc_html($order->billing_data['state']); ?>
                    <?php endif; ?>
                    <?php if ($order->billing_data['postcode']): ?>
                        <?php echo esc_html($order->billing_data['postcode']); ?>
                    <?php endif; ?><br>
                    <?php if ($order->billing_data['country']): ?>
                        <?php echo esc_html($order->billing_data['country']); ?><br>
                    <?php endif; ?>
                    <br>
                    <?php _e('Email:', 'smartshop'); ?> <?php echo esc_html($order->billing_data['email']); ?><br>
                    <?php _e('Phone:', 'smartshop'); ?> <?php echo esc_html($order->billing_data['phone']); ?>
                </div>
            </div>
            
            <!-- Payment Instructions -->
            <?php if ($order->payment_method === 'cod'): ?>
                <div class="payment-info">
                    <h4><?php _e('Payment Instructions', 'smartshop'); ?></h4>
                    <p><?php echo esc_html(SmartShop_Settings::get_option('cod_instructions', __('Pay with cash upon delivery.', 'smartshop'))); ?></p>
                </div>
            <?php elseif ($order->payment_method === 'manual_payment'): ?>
                <div class="payment-info">
                    <h4><?php _e('Payment Instructions', 'smartshop'); ?></h4>
                    <p><?php echo nl2br(esc_html(SmartShop_Settings::get_option('manual_payment_instructions', __('Please transfer the amount to our bank account and send us the receipt.', 'smartshop')))); ?></p>
                </div>
            <?php endif; ?>
            
            <!-- Order Notes -->
            <?php if ($order->order_notes): ?>
                <div class="order-info">
                    <h2><?php _e('Order Notes', 'smartshop'); ?></h2>
                    <p><?php echo nl2br(esc_html($order->order_notes)); ?></p>
                </div>
            <?php endif; ?>
            
            <!-- Call to Action -->
            <div style="text-align: center; margin: 30px 0;">
                <a href="<?php echo esc_url($shop_url); ?>" class="button">
                    <?php _e('Continue Shopping', 'smartshop'); ?>
                </a>
            </div>
            
            <!-- Contact Information -->
            <p><?php _e('If you have any questions about your order, please contact us:', 'smartshop'); ?></p>
            <p>
                <?php _e('Email:', 'smartshop'); ?> <a href="mailto:<?php echo esc_attr(SmartShop_Settings::get_option('admin_email')); ?>"><?php echo esc_html(SmartShop_Settings::get_option('admin_email')); ?></a><br>
                <?php _e('Website:', 'smartshop'); ?> <a href="<?php echo esc_url(home_url()); ?>"><?php echo esc_html(get_bloginfo('name')); ?></a>
            </p>
            
        </div>
        
        <!-- Email Footer -->
        <div class="email-footer">
            <p><?php printf(__('Thank you for shopping with %s!', 'smartshop'), esc_html($shop_name)); ?></p>
            <p><?php printf(__('This email was sent from %s', 'smartshop'), '<a href="' . esc_url(home_url()) . '">' . esc_html(get_bloginfo('name')) . '</a>'); ?></p>
        </div>
        
    </div>
</body>
</html>
