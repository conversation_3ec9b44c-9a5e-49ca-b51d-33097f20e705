<?php
/**
 * Order Status Update Email Template
 * 
 * This template is used for order status update emails sent to customers
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$shop_name = SmartShop_Settings::get_option('shop_name', get_bloginfo('name'));
$shop_url = SmartShop_Public::get_shop_url();
$primary_color = SmartShop_Settings::get_option('primary_color', '#3b82f6');

// Status colors
$status_colors = array(
    'pending' => '#ffc107',
    'processing' => '#17a2b8',
    'completed' => '#28a745',
    'cancelled' => '#dc3545'
);

$status_color = isset($status_colors[$order->status]) ? $status_colors[$order->status] : $primary_color;

// Status messages
$status_messages = array(
    'pending' => __('Your order is pending and will be processed soon.', 'smartshop'),
    'processing' => __('Your order is being processed and will be shipped soon.', 'smartshop'),
    'completed' => __('Your order has been completed successfully!', 'smartshop'),
    'cancelled' => __('Your order has been cancelled.', 'smartshop')
);

$status_message = isset($status_messages[$order->status]) ? $status_messages[$order->status] : __('Your order status has been updated.', 'smartshop');
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php printf(__('Order Status Update - %s', 'smartshop'), $order->order_number); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .email-header {
            background-color: <?php echo esc_attr($status_color); ?>;
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .email-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: normal;
        }
        .status-badge {
            background-color: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 10px;
            display: inline-block;
        }
        .email-body {
            padding: 30px 20px;
        }
        .status-update {
            background-color: #f8f9fa;
            border-left: 4px solid <?php echo esc_attr($status_color); ?>;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 6px 6px 0;
        }
        .status-update h2 {
            margin: 0 0 10px 0;
            font-size: 18px;
            color: <?php echo esc_attr($status_color); ?>;
        }
        .status-update p {
            margin: 0;
            font-size: 16px;
        }
        .order-info {
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .order-info h3 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #333;
        }
        .order-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .order-details:last-child {
            margin-bottom: 0;
        }
        .order-details strong {
            color: #333;
        }
        .order-summary {
            background-color: #fff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .order-summary h3 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #333;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 10px;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .summary-row.total {
            font-weight: 600;
            font-size: 18px;
            color: <?php echo esc_attr($status_color); ?>;
            border-top: 1px solid #e9ecef;
            padding-top: 10px;
            margin-top: 10px;
        }
        .next-steps {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .next-steps h3 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #0066cc;
        }
        .next-steps ul {
            margin: 0;
            padding-left: 20px;
        }
        .next-steps li {
            margin-bottom: 8px;
        }
        .email-footer {
            background-color: #f9f9f9;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #eee;
        }
        .email-footer p {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }
        .button {
            display: inline-block;
            background-color: <?php echo esc_attr($status_color); ?>;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 15px 0;
        }
        .button:hover {
            opacity: 0.9;
        }
        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            .email-body {
                padding: 20px 15px;
            }
            .order-details {
                flex-direction: column;
            }
            .summary-row {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        
        <!-- Email Header -->
        <div class="email-header">
            <h1><?php _e('Order Status Update', 'smartshop'); ?></h1>
            <div class="status-badge">
                <?php echo esc_html(ucfirst($order->status)); ?>
            </div>
        </div>
        
        <!-- Email Body -->
        <div class="email-body">
            
            <!-- Greeting -->
            <p><?php printf(__('Hi %s,', 'smartshop'), esc_html($order->billing_data['first_name'])); ?></p>
            
            <!-- Status Update -->
            <div class="status-update">
                <h2><?php _e('Your Order Status Has Been Updated', 'smartshop'); ?></h2>
                <p><?php echo esc_html($status_message); ?></p>
            </div>
            
            <!-- Order Information -->
            <div class="order-info">
                <h3><?php _e('Order Details', 'smartshop'); ?></h3>
                
                <div class="order-details">
                    <span><strong><?php _e('Order Number:', 'smartshop'); ?></strong></span>
                    <span><?php echo esc_html($order->order_number); ?></span>
                </div>
                
                <div class="order-details">
                    <span><strong><?php _e('Order Date:', 'smartshop'); ?></strong></span>
                    <span><?php echo date_i18n(get_option('date_format'), strtotime($order->created_at)); ?></span>
                </div>
                
                <div class="order-details">
                    <span><strong><?php _e('Payment Method:', 'smartshop'); ?></strong></span>
                    <span><?php echo esc_html(ucfirst(str_replace('_', ' ', $order->payment_method))); ?></span>
                </div>
                
                <div class="order-details">
                    <span><strong><?php _e('Current Status:', 'smartshop'); ?></strong></span>
                    <span style="color: <?php echo esc_attr($status_color); ?>; font-weight: 600;">
                        <?php echo esc_html(ucfirst($order->status)); ?>
                    </span>
                </div>
            </div>
            
            <!-- Order Summary -->
            <div class="order-summary">
                <h3><?php _e('Order Summary', 'smartshop'); ?></h3>
                
                <?php foreach ($order->items as $item): ?>
                    <div class="summary-row">
                        <span><?php echo esc_html($item->product_name); ?> × <?php echo esc_html($item->quantity); ?></span>
                        <span><?php echo SmartShop_Settings::format_currency($item->total); ?></span>
                    </div>
                <?php endforeach; ?>
                
                <div class="summary-row total">
                    <span><?php _e('Total:', 'smartshop'); ?></span>
                    <span><?php echo SmartShop_Settings::format_currency($order->total); ?></span>
                </div>
            </div>
            
            <!-- Next Steps -->
            <?php if ($order->status === 'processing'): ?>
                <div class="next-steps">
                    <h3><?php _e('What happens next?', 'smartshop'); ?></h3>
                    <ul>
                        <li><?php _e('Your order is being prepared for shipment', 'smartshop'); ?></li>
                        <li><?php _e('You will receive a tracking number once shipped', 'smartshop'); ?></li>
                        <li><?php _e('Estimated delivery time: 3-5 business days', 'smartshop'); ?></li>
                    </ul>
                </div>
            <?php elseif ($order->status === 'completed'): ?>
                <div class="next-steps">
                    <h3><?php _e('Thank you for your purchase!', 'smartshop'); ?></h3>
                    <ul>
                        <li><?php _e('Your order has been delivered successfully', 'smartshop'); ?></li>
                        <li><?php _e('We hope you enjoy your purchase', 'smartshop'); ?></li>
                        <li><?php _e('Please consider leaving a review', 'smartshop'); ?></li>
                    </ul>
                </div>
            <?php elseif ($order->status === 'cancelled'): ?>
                <div class="next-steps">
                    <h3><?php _e('Order Cancellation', 'smartshop'); ?></h3>
                    <ul>
                        <li><?php _e('Your order has been cancelled as requested', 'smartshop'); ?></li>
                        <li><?php _e('Any payment will be refunded within 3-5 business days', 'smartshop'); ?></li>
                        <li><?php _e('Feel free to place a new order anytime', 'smartshop'); ?></li>
                    </ul>
                </div>
            <?php endif; ?>
            
            <!-- Call to Action -->
            <div style="text-align: center; margin: 30px 0;">
                <?php if ($order->status !== 'cancelled'): ?>
                    <a href="<?php echo esc_url($shop_url); ?>" class="button">
                        <?php _e('Continue Shopping', 'smartshop'); ?>
                    </a>
                <?php else: ?>
                    <a href="<?php echo esc_url($shop_url); ?>" class="button">
                        <?php _e('Browse Products', 'smartshop'); ?>
                    </a>
                <?php endif; ?>
            </div>
            
            <!-- Contact Information -->
            <p><?php _e('If you have any questions about your order, please don\'t hesitate to contact us:', 'smartshop'); ?></p>
            <p>
                <?php _e('Email:', 'smartshop'); ?> <a href="mailto:<?php echo esc_attr(SmartShop_Settings::get_option('admin_email')); ?>"><?php echo esc_html(SmartShop_Settings::get_option('admin_email')); ?></a><br>
                <?php _e('Website:', 'smartshop'); ?> <a href="<?php echo esc_url(home_url()); ?>"><?php echo esc_html(get_bloginfo('name')); ?></a>
            </p>
            
        </div>
        
        <!-- Email Footer -->
        <div class="email-footer">
            <p><?php printf(__('Thank you for shopping with %s!', 'smartshop'), esc_html($shop_name)); ?></p>
            <p><?php printf(__('This email was sent from %s', 'smartshop'), '<a href="' . esc_url(home_url()) . '">' . esc_html(get_bloginfo('name')) . '</a>'); ?></p>
        </div>
        
    </div>
</body>
</html>
