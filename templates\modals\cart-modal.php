<?php
/**
 * Cart Modal Template
 * 
 * Displays the shopping cart in a modal popup
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$cart = SmartShop_Cart::get_instance();
$cart_items = $cart->get_cart_items();
$cart_total = $cart->get_cart_total();
$cart_count = $cart->get_cart_count();
?>

<div class="smartshop-cart-modal" id="smartshop-cart-modal">
    <div class="cart-modal-overlay"></div>
    <div class="cart-modal-content">
        
        <!-- Modal Header -->
        <div class="cart-modal-header">
            <h3 class="cart-modal-title">
                <?php _e('Shopping Cart', 'smartshop'); ?>
                <span class="cart-count-badge">(<?php echo $cart_count; ?>)</span>
            </h3>
            <button class="cart-modal-close" type="button" aria-label="<?php _e('Close cart', 'smartshop'); ?>">
                &times;
            </button>
        </div>
        
        <!-- Modal Body -->
        <div class="cart-modal-body">
            <?php if (!empty($cart_items)): ?>
                
                <div class="cart-items-list">
                    <?php foreach ($cart_items as $item): ?>
                        <div class="cart-item" data-product-id="<?php echo esc_attr($item['product_id']); ?>">
                            
                            <!-- Product Image -->
                            <div class="cart-item-image">
                                <?php if ($item['product']->image_url): ?>
                                    <img src="<?php echo esc_url($item['product']->image_url); ?>" 
                                         alt="<?php echo esc_attr($item['product']->name); ?>">
                                <?php else: ?>
                                    <div class="image-placeholder">
                                        <span><?php _e('No Image', 'smartshop'); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Product Details -->
                            <div class="cart-item-details">
                                <h4 class="cart-item-name">
                                    <a href="<?php echo esc_url(SmartShop_Public::get_product_url($item['product']->slug)); ?>">
                                        <?php echo esc_html($item['product']->name); ?>
                                    </a>
                                </h4>
                                
                                <div class="cart-item-price">
                                    <?php echo SmartShop_Settings::format_currency($item['price']); ?>
                                    <?php if ($item['quantity'] > 1): ?>
                                        <span class="price-each">
                                            (<?php printf(__('%s each', 'smartshop'), SmartShop_Settings::format_currency($item['price'])); ?>)
                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Quantity Controls -->
                                <div class="cart-item-quantity">
                                    <button type="button" class="quantity-btn quantity-decrease" 
                                            data-product-id="<?php echo esc_attr($item['product_id']); ?>"
                                            <?php echo $item['quantity'] <= 1 ? 'disabled' : ''; ?>>
                                        -
                                    </button>
                                    
                                    <input type="number" 
                                           class="quantity-input" 
                                           value="<?php echo esc_attr($item['quantity']); ?>"
                                           min="1" 
                                           max="<?php echo esc_attr($item['product']->stock_quantity ?: 999); ?>"
                                           data-product-id="<?php echo esc_attr($item['product_id']); ?>">
                                    
                                    <button type="button" class="quantity-btn quantity-increase"
                                            data-product-id="<?php echo esc_attr($item['product_id']); ?>"
                                            <?php echo ($item['product']->stock_quantity && $item['quantity'] >= $item['product']->stock_quantity) ? 'disabled' : ''; ?>>
                                        +
                                    </button>
                                </div>
                                
                                <!-- Item Total -->
                                <div class="cart-item-total">
                                    <strong><?php echo SmartShop_Settings::format_currency($item['total']); ?></strong>
                                </div>
                            </div>
                            
                            <!-- Remove Button -->
                            <button type="button" class="cart-item-remove" 
                                    data-product-id="<?php echo esc_attr($item['product_id']); ?>"
                                    aria-label="<?php _e('Remove item', 'smartshop'); ?>">
                                &times;
                            </button>
                            
                        </div>
                    <?php endforeach; ?>
                </div>
                
            <?php else: ?>
                
                <!-- Empty Cart -->
                <div class="cart-empty">
                    <div class="empty-cart-icon">🛒</div>
                    <h4><?php _e('Your cart is empty', 'smartshop'); ?></h4>
                    <p><?php _e('Add some products to get started!', 'smartshop'); ?></p>
                    <a href="<?php echo esc_url(SmartShop_Public::get_shop_url()); ?>" class="btn btn-primary">
                        <?php _e('Continue Shopping', 'smartshop'); ?>
                    </a>
                </div>
                
            <?php endif; ?>
        </div>
        
        <!-- Modal Footer -->
        <?php if (!empty($cart_items)): ?>
            <div class="cart-modal-footer">
                
                <!-- Cart Summary -->
                <div class="cart-summary">
                    <div class="summary-row subtotal">
                        <span class="summary-label"><?php _e('Subtotal:', 'smartshop'); ?></span>
                        <span class="summary-value"><?php echo SmartShop_Settings::format_currency($cart_total); ?></span>
                    </div>
                    
                    <?php if (SmartShop_Settings::get_option('enable_shipping', 0)): ?>
                        <div class="summary-row shipping">
                            <span class="summary-label"><?php _e('Shipping:', 'smartshop'); ?></span>
                            <span class="summary-value">
                                <?php
                                $shipping_cost = SmartShop_Settings::get_option('shipping_cost', 0);
                                $free_threshold = SmartShop_Settings::get_option('free_shipping_threshold', 0);
                                
                                if ($free_threshold > 0 && $cart_total >= $free_threshold) {
                                    _e('Free', 'smartshop');
                                } else {
                                    echo SmartShop_Settings::format_currency($shipping_cost);
                                }
                                ?>
                            </span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (SmartShop_Settings::get_option('enable_tax', 0)): ?>
                        <div class="summary-row tax">
                            <span class="summary-label"><?php _e('Tax:', 'smartshop'); ?></span>
                            <span class="summary-value">
                                <?php
                                $tax_rate = SmartShop_Settings::get_option('tax_rate', 0);
                                $tax_amount = ($cart_total * $tax_rate) / 100;
                                echo SmartShop_Settings::format_currency($tax_amount);
                                ?>
                            </span>
                        </div>
                    <?php endif; ?>
                    
                    <div class="summary-row total">
                        <span class="summary-label"><strong><?php _e('Total:', 'smartshop'); ?></strong></span>
                        <span class="summary-value">
                            <strong class="cart-total-amount">
                                <?php
                                $final_total = $cart_total;
                                
                                // Add shipping if enabled
                                if (SmartShop_Settings::get_option('enable_shipping', 0)) {
                                    $shipping_cost = SmartShop_Settings::get_option('shipping_cost', 0);
                                    $free_threshold = SmartShop_Settings::get_option('free_shipping_threshold', 0);
                                    
                                    if (!($free_threshold > 0 && $cart_total >= $free_threshold)) {
                                        $final_total += $shipping_cost;
                                    }
                                }
                                
                                // Add tax if enabled
                                if (SmartShop_Settings::get_option('enable_tax', 0)) {
                                    $tax_rate = SmartShop_Settings::get_option('tax_rate', 0);
                                    $final_total += ($cart_total * $tax_rate) / 100;
                                }
                                
                                echo SmartShop_Settings::format_currency($final_total);
                                ?>
                            </strong>
                        </span>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="cart-actions">
                    <a href="<?php echo esc_url(SmartShop_Public::get_cart_url()); ?>" 
                       class="btn btn-secondary btn-block">
                        <?php _e('View Full Cart', 'smartshop'); ?>
                    </a>
                    
                    <a href="<?php echo esc_url(SmartShop_Public::get_checkout_url()); ?>" 
                       class="btn btn-primary btn-block">
                        <?php _e('Proceed to Checkout', 'smartshop'); ?>
                    </a>
                </div>
                
                <!-- Continue Shopping -->
                <div class="continue-shopping">
                    <button type="button" class="continue-shopping-btn cart-modal-close">
                        <?php _e('Continue Shopping', 'smartshop'); ?>
                    </button>
                </div>
                
            </div>
        <?php endif; ?>
        
    </div>
</div>

<style>
/* Cart Modal Specific Styles */
.cart-count-badge {
    font-size: 0.875rem;
    font-weight: normal;
    color: var(--smartshop-text-light);
}

.cart-items-list {
    max-height: 400px;
    overflow-y: auto;
}

.cart-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--smartshop-border-color);
    position: relative;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-image {
    width: 80px;
    height: 80px;
    border-radius: var(--smartshop-border-radius);
    overflow: hidden;
    flex-shrink: 0;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    background-color: var(--smartshop-bg-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    color: var(--smartshop-text-light);
}

.cart-item-details {
    flex: 1;
    min-width: 0;
}

.cart-item-name {
    margin: 0 0 0.5rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1.4;
}

.cart-item-name a {
    color: var(--smartshop-text-color);
    text-decoration: none;
    transition: var(--smartshop-transition);
}

.cart-item-name a:hover {
    color: var(--smartshop-primary-color);
}

.cart-item-price {
    font-size: 0.875rem;
    color: var(--smartshop-text-color);
    margin-bottom: 0.5rem;
}

.price-each {
    font-size: 0.75rem;
    color: var(--smartshop-text-light);
}

.cart-item-quantity {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.cart-item-total {
    font-size: 0.875rem;
    color: var(--smartshop-primary-color);
}

.cart-item-remove {
    position: absolute;
    top: 0.5rem;
    right: 0;
    background: none;
    border: none;
    color: var(--smartshop-text-light);
    cursor: pointer;
    font-size: 1.25rem;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--smartshop-border-radius);
    transition: var(--smartshop-transition);
}

.cart-item-remove:hover {
    background-color: var(--smartshop-error-color);
    color: white;
}

.cart-empty {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-cart-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.cart-empty h4 {
    margin-bottom: 0.5rem;
    color: var(--smartshop-text-color);
}

.cart-empty p {
    color: var(--smartshop-text-light);
    margin-bottom: 2rem;
}

.cart-summary {
    margin-bottom: 1.5rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.summary-row.total {
    border-top: 1px solid var(--smartshop-border-color);
    padding-top: 1rem;
    margin-top: 0.5rem;
    font-size: 1.125rem;
}

.cart-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.btn-block {
    width: 100%;
}

.continue-shopping {
    text-align: center;
}

.continue-shopping-btn {
    background: none;
    border: none;
    color: var(--smartshop-text-light);
    cursor: pointer;
    font-size: 0.875rem;
    text-decoration: underline;
    transition: var(--smartshop-transition);
}

.continue-shopping-btn:hover {
    color: var(--smartshop-primary-color);
}

@media (max-width: 480px) {
    .cart-modal-content {
        margin: 0;
        height: 100vh;
        max-height: none;
        border-radius: 0;
    }
    
    .cart-item {
        gap: 0.75rem;
    }
    
    .cart-item-image {
        width: 60px;
        height: 60px;
    }
    
    .cart-item-name {
        font-size: 0.8rem;
    }
    
    .cart-item-price,
    .cart-item-total {
        font-size: 0.8rem;
    }
}
</style>
