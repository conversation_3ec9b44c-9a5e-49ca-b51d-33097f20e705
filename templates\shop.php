<?php
/**
 * Shop Template
 * 
 * Displays the main shop page with products, filters, and search
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header();

// Get current page and filters
$current_page = max(1, intval($_GET['page'] ?? 1));
$search = sanitize_text_field($_GET['search'] ?? '');
$category_id = intval($_GET['category'] ?? 0) ?: null;
$min_price = floatval($_GET['min_price'] ?? 0) ?: null;
$max_price = floatval($_GET['max_price'] ?? 0) ?: null;
$in_stock = isset($_GET['in_stock']) ? (bool)$_GET['in_stock'] : null;
$orderby = sanitize_text_field($_GET['orderby'] ?? 'created_at');
$order = sanitize_text_field($_GET['order'] ?? 'DESC');

// Products per page
$per_page = SmartShop_Settings::get_option('products_per_page', 12);
$offset = ($current_page - 1) * $per_page;

// Build query args
$args = array(
    'limit' => $per_page,
    'offset' => $offset,
    'search' => $search,
    'category_id' => $category_id,
    'min_price' => $min_price,
    'max_price' => $max_price,
    'in_stock' => $in_stock,
    'orderby' => $orderby,
    'order' => $order
);

// Get products and total count
$products = SmartShop_Product::get_products($args);
$total_products = SmartShop_Product::get_product_count($args);
$total_pages = ceil($total_products / $per_page);

// Get categories for filters
$categories = SmartShop_Shortcodes::get_categories();

// Breadcrumbs
$breadcrumbs = array(
    array('title' => __('Home', 'smartshop'), 'url' => home_url()),
    array('title' => SmartShop_Settings::get_option('shop_page_title', __('Shop', 'smartshop')))
);

if ($category_id) {
    $category = SmartShop_Shortcodes::get_categories(array('limit' => 1, 'category_id' => $category_id));
    if (!empty($category)) {
        $breadcrumbs[] = array('title' => $category[0]->name);
    }
}
?>

<div class="smartshop-page smartshop-shop">
    <div class="smartshop-container">
        
        <?php if (SmartShop_Settings::get_option('show_breadcrumbs', 1)): ?>
            <?php echo SmartShop_Shortcodes::get_breadcrumbs($breadcrumbs); ?>
        <?php endif; ?>
        
        <!-- Page Header -->
        <div class="shop-header">
            <h1 class="shop-title">
                <?php echo esc_html(SmartShop_Settings::get_option('shop_page_title', __('Shop', 'smartshop'))); ?>
            </h1>
            
            <?php if (SmartShop_Settings::get_option('shop_page_description')): ?>
                <p class="shop-description">
                    <?php echo esc_html(SmartShop_Settings::get_option('shop_page_description')); ?>
                </p>
            <?php endif; ?>
        </div>
        
        <!-- Search Bar -->
        <?php if (SmartShop_Settings::get_option('enable_search', 1)): ?>
            <div class="shop-search">
                <?php echo SmartShop_Frontend::get_search_form(); ?>
            </div>
        <?php endif; ?>
        
        <div class="smartshop-row">
            
            <!-- Sidebar Filters -->
            <?php if (SmartShop_Settings::get_option('enable_filters', 1)): ?>
                <div class="smartshop-col smartshop-col-3">
                    <aside class="shop-sidebar">
                        <?php echo SmartShop_Frontend::get_filters_sidebar(); ?>
                    </aside>
                </div>
            <?php endif; ?>
            
            <!-- Main Content -->
            <div class="smartshop-col <?php echo SmartShop_Settings::get_option('enable_filters', 1) ? 'smartshop-col-9' : 'smartshop-col-12'; ?>">
                
                <!-- Toolbar -->
                <div class="shop-toolbar">
                    <div class="toolbar-left">
                        <?php if (SmartShop_Settings::get_option('show_product_count', 1)): ?>
                            <div class="results-count">
                                <?php
                                $start = $offset + 1;
                                $end = min($offset + $per_page, $total_products);
                                printf(
                                    __('Showing %d-%d of %d results', 'smartshop'),
                                    $start,
                                    $end,
                                    $total_products
                                );
                                ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="toolbar-right">
                        <?php if (SmartShop_Settings::get_option('show_sorting', 1)): ?>
                            <div class="shop-sorting">
                                <label for="shop-orderby"><?php _e('Sort by:', 'smartshop'); ?></label>
                                <select id="shop-orderby" name="orderby" onchange="this.form.submit()">
                                    <option value="created_at" <?php selected($orderby, 'created_at'); ?>>
                                        <?php _e('Latest', 'smartshop'); ?>
                                    </option>
                                    <option value="name" <?php selected($orderby, 'name'); ?>>
                                        <?php _e('Name', 'smartshop'); ?>
                                    </option>
                                    <option value="price" <?php selected($orderby, 'price'); ?>>
                                        <?php _e('Price: Low to High', 'smartshop'); ?>
                                    </option>
                                    <option value="price_desc" <?php selected($orderby, 'price_desc'); ?>>
                                        <?php _e('Price: High to Low', 'smartshop'); ?>
                                    </option>
                                </select>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Layout Toggle -->
                        <div class="layout-toggle">
                            <button class="layout-btn grid-view active" data-layout="grid" title="<?php _e('Grid View', 'smartshop'); ?>">
                                <span class="grid-icon">⊞</span>
                            </button>
                            <button class="layout-btn list-view" data-layout="list" title="<?php _e('List View', 'smartshop'); ?>">
                                <span class="list-icon">☰</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Products Grid -->
                <?php if (!empty($products)): ?>
                    <div class="shop-products">
                        <?php
                        $columns = SmartShop_Settings::get_option('grid_columns_desktop', 4);
                        echo SmartShop_Frontend::get_product_grid($products, $columns);
                        ?>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <div class="shop-pagination">
                            <?php echo SmartShop_Frontend::get_pagination($current_page, $total_pages, SmartShop_Public::get_shop_url()); ?>
                        </div>
                    <?php endif; ?>
                    
                <?php else: ?>
                    
                    <!-- No Products Found -->
                    <div class="no-products-found">
                        <div class="no-products-content">
                            <h3><?php _e('No products found', 'smartshop'); ?></h3>
                            <p><?php _e('Sorry, no products match your search criteria.', 'smartshop'); ?></p>
                            
                            <?php if ($search || $category_id || $min_price || $max_price): ?>
                                <a href="<?php echo esc_url(SmartShop_Public::get_shop_url()); ?>" class="btn btn-primary">
                                    <?php _e('Clear Filters', 'smartshop'); ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                <?php endif; ?>
                
            </div>
            
        </div>
        
    </div>
</div>

<!-- Cart Modal -->
<?php if (SmartShop_Settings::get_option('enable_ajax_cart', 1)): ?>
    <div class="smartshop-cart-modal">
        <div class="cart-modal-overlay"></div>
        <div class="cart-modal-content">
            <div class="cart-modal-header">
                <h3 class="cart-modal-title"><?php _e('Shopping Cart', 'smartshop'); ?></h3>
                <button class="cart-modal-close">&times;</button>
            </div>
            <div class="cart-modal-body">
                <!-- Cart items will be loaded here via AJAX -->
            </div>
            <div class="cart-modal-footer">
                <div class="cart-total">
                    <span><?php _e('Total:', 'smartshop'); ?></span>
                    <span class="total-amount"><?php echo SmartShop_Settings::format_currency(0); ?></span>
                </div>
                <div class="cart-actions">
                    <a href="<?php echo esc_url(SmartShop_Public::get_cart_url()); ?>" class="btn btn-secondary">
                        <?php _e('View Cart', 'smartshop'); ?>
                    </a>
                    <a href="<?php echo esc_url(SmartShop_Public::get_checkout_url()); ?>" class="btn btn-primary">
                        <?php _e('Checkout', 'smartshop'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<style>
/* Shop-specific styles */
.shop-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem 0;
}

.shop-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--smartshop-text-color);
}

.shop-description {
    font-size: 1.125rem;
    color: var(--smartshop-text-light);
    max-width: 600px;
    margin: 0 auto;
}

.shop-search {
    margin-bottom: 2rem;
}

.shop-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--smartshop-bg-light);
    border-radius: var(--smartshop-border-radius);
}

.results-count {
    font-size: 0.875rem;
    color: var(--smartshop-text-light);
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.shop-sorting select {
    padding: 0.5rem;
    border: 1px solid var(--smartshop-border-color);
    border-radius: var(--smartshop-border-radius);
    font-size: 0.875rem;
}

.layout-toggle {
    display: flex;
    gap: 0.25rem;
}

.layout-btn {
    padding: 0.5rem;
    border: 1px solid var(--smartshop-border-color);
    background: var(--smartshop-bg-color);
    border-radius: var(--smartshop-border-radius);
    cursor: pointer;
    transition: var(--smartshop-transition);
}

.layout-btn:hover,
.layout-btn.active {
    background: var(--smartshop-primary-color);
    color: white;
    border-color: var(--smartshop-primary-color);
}

.no-products-found {
    text-align: center;
    padding: 4rem 2rem;
}

.no-products-content h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--smartshop-text-color);
}

.no-products-content p {
    color: var(--smartshop-text-light);
    margin-bottom: 2rem;
}

@media (max-width: 768px) {
    .shop-toolbar {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .toolbar-right {
        justify-content: space-between;
    }
    
    .shop-title {
        font-size: 2rem;
    }
}
</style>

<script>
// Shop-specific JavaScript
jQuery(document).ready(function($) {
    // Layout toggle
    $('.layout-btn').on('click', function() {
        var layout = $(this).data('layout');
        
        $('.layout-btn').removeClass('active');
        $(this).addClass('active');
        
        var $grid = $('.smartshop-product-grid');
        $grid.removeClass('layout-grid layout-list').addClass('layout-' + layout);
        
        // Save preference
        localStorage.setItem('smartshop_layout', layout);
    });
    
    // Load saved layout preference
    var savedLayout = localStorage.getItem('smartshop_layout');
    if (savedLayout) {
        $('.layout-btn[data-layout="' + savedLayout + '"]').click();
    }
    
    // Sorting change
    $('#shop-orderby').on('change', function() {
        var orderby = $(this).val();
        var url = new URL(window.location.href);
        
        if (orderby === 'price_desc') {
            url.searchParams.set('orderby', 'price');
            url.searchParams.set('order', 'DESC');
        } else {
            url.searchParams.set('orderby', orderby);
            url.searchParams.set('order', 'ASC');
        }
        
        window.location.href = url.toString();
    });
});
</script>

<?php get_footer(); ?>
