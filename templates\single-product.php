<?php
/**
 * Single Product Template
 * 
 * Displays individual product page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header();

// Get product from global variable set in SmartShop_Public
global $smartshop_product;
$product = $smartshop_product;

if (!$product) {
    get_template_part('404');
    get_footer();
    return;
}

// Get product category
$category = null;
if ($product->category_id) {
    $category = SmartShop_Category::get_category($product->category_id);
}

// Get related products
$related_products = SmartShop_Product::get_products(array(
    'category_id' => $product->category_id,
    'exclude' => array($product->id),
    'limit' => 4,
    'status' => 'publish'
));
?>

<div class="smartshop-container">
    <div class="smartshop-product-wrapper">
        
        <!-- Product Header -->
        <div class="smartshop-product-header">
            <div class="smartshop-breadcrumbs">
                <a href="<?php echo home_url(); ?>"><?php _e('Home', 'smartshop'); ?></a>
                <span class="separator">/</span>
                <a href="<?php echo SmartShop_Public::get_shop_url(); ?>"><?php _e('Shop', 'smartshop'); ?></a>
                <?php if ($category): ?>
                    <span class="separator">/</span>
                    <a href="<?php echo SmartShop_Public::get_category_url($category->slug); ?>"><?php echo esc_html($category->name); ?></a>
                <?php endif; ?>
                <span class="separator">/</span>
                <span class="current"><?php echo esc_html($product->name); ?></span>
            </div>
        </div>

        <div class="smartshop-product-content">
            
            <div class="product-main">
                
                <!-- Product Images -->
                <div class="product-images">
                    <div class="product-image-main">
                        <?php if ($product->image_url): ?>
                            <img src="<?php echo esc_url($product->image_url); ?>" alt="<?php echo esc_attr($product->name); ?>" id="main-product-image">
                        <?php else: ?>
                            <div class="no-image">
                                <svg width="200" height="200" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                    <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                    <polyline points="21,15 16,10 5,21"></polyline>
                                </svg>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($product->featured): ?>
                            <span class="featured-badge"><?php _e('Featured', 'smartshop'); ?></span>
                        <?php endif; ?>
                        
                        <?php if ($product->sale_price && $product->sale_price < $product->price): ?>
                            <span class="sale-badge">
                                <?php 
                                $discount_percent = round((($product->price - $product->sale_price) / $product->price) * 100);
                                printf(__('-%d%%', 'smartshop'), $discount_percent);
                                ?>
                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Additional product images would go here -->
                    <?php do_action('smartshop_product_images', $product); ?>
                </div>
                
                <!-- Product Info -->
                <div class="product-summary">
                    
                    <h1 class="product-title"><?php echo esc_html($product->name); ?></h1>
                    
                    <?php if ($product->sku): ?>
                        <div class="product-sku">
                            <span class="sku-label"><?php _e('SKU:', 'smartshop'); ?></span>
                            <span class="sku-value"><?php echo esc_html($product->sku); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Product Price -->
                    <div class="product-price">
                        <?php if ($product->sale_price && $product->sale_price < $product->price): ?>
                            <span class="sale-price"><?php echo SmartShop_Settings::format_currency($product->sale_price); ?></span>
                            <span class="regular-price"><?php echo SmartShop_Settings::format_currency($product->price); ?></span>
                            <span class="savings">
                                <?php printf(__('Save %s', 'smartshop'), SmartShop_Settings::format_currency($product->price - $product->sale_price)); ?>
                            </span>
                        <?php else: ?>
                            <span class="price"><?php echo SmartShop_Settings::format_currency($product->price); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Product Description -->
                    <?php if ($product->short_description): ?>
                        <div class="product-short-description">
                            <p><?php echo wp_kses_post($product->short_description); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Stock Status -->
                    <div class="product-stock">
                        <?php if ($product->stock_status === 'instock'): ?>
                            <span class="in-stock">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="20,6 9,17 4,12"></polyline>
                                </svg>
                                <?php _e('In Stock', 'smartshop'); ?>
                            </span>
                            <?php if ($product->manage_stock && $product->stock_quantity): ?>
                                <span class="stock-quantity">
                                    (<?php printf(__('%d available', 'smartshop'), $product->stock_quantity); ?>)
                                </span>
                            <?php endif; ?>
                        <?php else: ?>
                            <span class="out-of-stock">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="18" y1="6" x2="6" y2="18"></line>
                                    <line x1="6" y1="6" x2="18" y2="18"></line>
                                </svg>
                                <?php _e('Out of Stock', 'smartshop'); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Add to Cart Form -->
                    <?php if ($product->stock_status === 'instock'): ?>
                        <form class="cart-form" data-product-id="<?php echo $product->id; ?>">
                            <div class="quantity-input">
                                <label for="quantity"><?php _e('Quantity:', 'smartshop'); ?></label>
                                <div class="quantity-controls">
                                    <button type="button" class="quantity-minus">-</button>
                                    <input type="number" id="quantity" name="quantity" value="1" min="1" max="<?php echo $product->stock_quantity ?: 999; ?>">
                                    <button type="button" class="quantity-plus">+</button>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-large add-to-cart-btn">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="9" cy="21" r="1"></circle>
                                    <circle cx="20" cy="21" r="1"></circle>
                                    <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                                </svg>
                                <?php _e('Add to Cart', 'smartshop'); ?>
                            </button>
                        </form>
                    <?php else: ?>
                        <div class="out-of-stock-message">
                            <button class="btn btn-secondary btn-large" disabled>
                                <?php _e('Out of Stock', 'smartshop'); ?>
                            </button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Product Meta -->
                    <div class="product-meta">
                        <?php if ($category): ?>
                            <div class="product-category">
                                <span class="meta-label"><?php _e('Category:', 'smartshop'); ?></span>
                                <a href="<?php echo SmartShop_Public::get_category_url($category->slug); ?>"><?php echo esc_html($category->name); ?></a>
                            </div>
                        <?php endif; ?>
                        
                        <div class="product-share">
                            <span class="meta-label"><?php _e('Share:', 'smartshop'); ?></span>
                            <div class="share-buttons">
                                <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>" target="_blank" class="share-facebook" title="<?php _e('Share on Facebook', 'smartshop'); ?>">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                    </svg>
                                </a>
                                <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode($product->name); ?>" target="_blank" class="share-twitter" title="<?php _e('Share on Twitter', 'smartshop'); ?>">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                    </svg>
                                </a>
                                <a href="https://wa.me/?text=<?php echo urlencode($product->name . ' - ' . get_permalink()); ?>" target="_blank" class="share-whatsapp" title="<?php _e('Share on WhatsApp', 'smartshop'); ?>">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.085"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                </div>
                
            </div>
            
            <!-- Product Tabs -->
            <div class="product-tabs">
                <div class="tab-nav">
                    <button class="tab-button active" data-tab="description"><?php _e('Description', 'smartshop'); ?></button>
                    <?php if (SmartShop_Settings::get_option('enable_reviews', false)): ?>
                        <button class="tab-button" data-tab="reviews"><?php _e('Reviews', 'smartshop'); ?></button>
                    <?php endif; ?>
                    <button class="tab-button" data-tab="additional"><?php _e('Additional Information', 'smartshop'); ?></button>
                </div>
                
                <div class="tab-content">
                    <div class="tab-panel active" id="tab-description">
                        <?php if ($product->description): ?>
                            <div class="product-description">
                                <?php echo wp_kses_post(wpautop($product->description)); ?>
                            </div>
                        <?php else: ?>
                            <p><?php _e('No description available for this product.', 'smartshop'); ?></p>
                        <?php endif; ?>
                    </div>
                    
                    <?php if (SmartShop_Settings::get_option('enable_reviews', false)): ?>
                    <div class="tab-panel" id="tab-reviews">
                        <div class="product-reviews">
                            <p><?php _e('Reviews functionality coming soon.', 'smartshop'); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="tab-panel" id="tab-additional">
                        <div class="additional-information">
                            <table class="product-attributes">
                                <?php if ($product->sku): ?>
                                <tr>
                                    <th><?php _e('SKU', 'smartshop'); ?></th>
                                    <td><?php echo esc_html($product->sku); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if ($category): ?>
                                <tr>
                                    <th><?php _e('Category', 'smartshop'); ?></th>
                                    <td><a href="<?php echo SmartShop_Public::get_category_url($category->slug); ?>"><?php echo esc_html($category->name); ?></a></td>
                                </tr>
                                <?php endif; ?>
                                <?php if ($product->weight): ?>
                                <tr>
                                    <th><?php _e('Weight', 'smartshop'); ?></th>
                                    <td><?php echo esc_html($product->weight); ?> <?php echo SmartShop_Settings::get_option('weight_unit', 'kg'); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if ($product->dimensions): ?>
                                <tr>
                                    <th><?php _e('Dimensions', 'smartshop'); ?></th>
                                    <td><?php echo esc_html($product->dimensions); ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Related Products -->
            <?php if (!empty($related_products)): ?>
            <div class="related-products">
                <h3><?php _e('Related Products', 'smartshop'); ?></h3>
                
                <div class="related-products-grid">
                    <?php foreach ($related_products as $related_product): ?>
                    <div class="related-product-card">
                        <div class="product-image">
                            <a href="<?php echo SmartShop_Public::get_product_url($related_product->slug); ?>">
                                <?php if ($related_product->image_url): ?>
                                    <img src="<?php echo esc_url($related_product->image_url); ?>" alt="<?php echo esc_attr($related_product->name); ?>" loading="lazy">
                                <?php else: ?>
                                    <div class="no-image">
                                        <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                            <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                            <polyline points="21,15 16,10 5,21"></polyline>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                            </a>
                        </div>
                        
                        <div class="product-info">
                            <h4 class="product-title">
                                <a href="<?php echo SmartShop_Public::get_product_url($related_product->slug); ?>">
                                    <?php echo esc_html($related_product->name); ?>
                                </a>
                            </h4>
                            
                            <div class="product-price">
                                <?php if ($related_product->sale_price && $related_product->sale_price < $related_product->price): ?>
                                    <span class="sale-price"><?php echo SmartShop_Settings::format_currency($related_product->sale_price); ?></span>
                                    <span class="regular-price"><?php echo SmartShop_Settings::format_currency($related_product->price); ?></span>
                                <?php else: ?>
                                    <span class="price"><?php echo SmartShop_Settings::format_currency($related_product->price); ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($related_product->stock_status === 'instock'): ?>
                                <button class="btn btn-primary btn-small add-to-cart-btn" data-product-id="<?php echo $related_product->id; ?>">
                                    <?php _e('Add to Cart', 'smartshop'); ?>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
            
        </div>
        
    </div>
</div>

<?php get_footer(); ?>
