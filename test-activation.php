<?php
/**
 * SmartShop Activation Test
 * 
 * This file tests the plugin activation step by step
 * Upload to WordPress root and access via browser
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('./wp-load.php');

echo '<h1>SmartShop Activation Test</h1>';

// Step 1: Check if main file exists
echo '<h2>Step 1: Main File Check</h2>';
$plugin_dir = WP_PLUGIN_DIR . '/smartshop';
$main_file = $plugin_dir . '/smartshop.php';

if (!file_exists($main_file)) {
    echo '<p>❌ Main plugin file not found: ' . $main_file . '</p>';
    exit;
}
echo '<p>✅ Main plugin file found</p>';

// Step 2: Test file inclusion
echo '<h2>Step 2: File Inclusion Test</h2>';
try {
    // Test including the main file
    ob_start();
    include_once $main_file;
    $output = ob_get_clean();
    
    if (!empty($output)) {
        echo '<p>⚠️ Output during inclusion (may indicate errors):</p>';
        echo '<pre>' . htmlspecialchars($output) . '</pre>';
    } else {
        echo '<p>✅ Main file included without output</p>';
    }
} catch (Exception $e) {
    echo '<p>❌ Error including main file: ' . $e->getMessage() . '</p>';
    exit;
} catch (ParseError $e) {
    echo '<p>❌ Parse error in main file: ' . $e->getMessage() . '</p>';
    exit;
} catch (Error $e) {
    echo '<p>❌ Fatal error in main file: ' . $e->getMessage() . '</p>';
    exit;
}

// Step 3: Check if main class exists
echo '<h2>Step 3: Main Class Check</h2>';
if (class_exists('SmartShop_Plugin')) {
    echo '<p>✅ SmartShop_Plugin class exists</p>';
} else {
    echo '<p>❌ SmartShop_Plugin class not found</p>';
    exit;
}

// Step 4: Test individual file inclusions
echo '<h2>Step 4: Individual File Tests</h2>';
$files_to_test = array(
    'includes/class-smartshop-database.php' => 'SmartShop_Database',
    'includes/class-smartshop-settings.php' => 'SmartShop_Settings',
    'includes/class-smartshop-product.php' => 'SmartShop_Product',
    'includes/class-smartshop-cart.php' => 'SmartShop_Cart',
    'includes/class-smartshop-order.php' => 'SmartShop_Order',
    'includes/class-smartshop-shortcodes.php' => 'SmartShop_Shortcodes',
    'admin/class-smartshop-admin.php' => 'SmartShop_Admin',
    'public/class-smartshop-public.php' => 'SmartShop_Public',
    'public/class-smartshop-frontend.php' => 'SmartShop_Frontend'
);

foreach ($files_to_test as $file => $class_name) {
    $file_path = $plugin_dir . '/' . $file;
    
    if (!file_exists($file_path)) {
        echo '<p>❌ File missing: ' . $file . '</p>';
        continue;
    }
    
    try {
        ob_start();
        include_once $file_path;
        $output = ob_get_clean();
        
        if (class_exists($class_name)) {
            echo '<p>✅ ' . $file . ' (' . $class_name . ')</p>';
        } else {
            echo '<p>⚠️ ' . $file . ' included but class ' . $class_name . ' not found</p>';
        }
        
        if (!empty($output)) {
            echo '<p>⚠️ Output from ' . $file . ':</p>';
            echo '<pre>' . htmlspecialchars($output) . '</pre>';
        }
        
    } catch (Exception $e) {
        echo '<p>❌ Error in ' . $file . ': ' . $e->getMessage() . '</p>';
    } catch (ParseError $e) {
        echo '<p>❌ Parse error in ' . $file . ': ' . $e->getMessage() . '</p>';
    } catch (Error $e) {
        echo '<p>❌ Fatal error in ' . $file . ': ' . $e->getMessage() . '</p>';
    }
}

// Step 5: Test database operations
echo '<h2>Step 5: Database Test</h2>';
if (class_exists('SmartShop_Database')) {
    try {
        // Test table creation
        $database = SmartShop_Database::get_instance();
        echo '<p>✅ Database instance created</p>';
        
        // Test table name generation
        $table_name = SmartShop_Database::get_table_name('products');
        echo '<p>✅ Table name generation works: ' . $table_name . '</p>';
        
    } catch (Exception $e) {
        echo '<p>❌ Database error: ' . $e->getMessage() . '</p>';
    }
} else {
    echo '<p>❌ SmartShop_Database class not available</p>';
}

// Step 6: Test plugin initialization
echo '<h2>Step 6: Plugin Initialization Test</h2>';
try {
    if (class_exists('SmartShop_Plugin')) {
        $plugin = SmartShop_Plugin::get_instance();
        echo '<p>✅ Plugin instance created successfully</p>';
    } else {
        echo '<p>❌ Cannot create plugin instance</p>';
    }
} catch (Exception $e) {
    echo '<p>❌ Plugin initialization error: ' . $e->getMessage() . '</p>';
} catch (Error $e) {
    echo '<p>❌ Fatal error during initialization: ' . $e->getMessage() . '</p>';
}

// Step 7: Check WordPress hooks
echo '<h2>Step 7: WordPress Hooks Test</h2>';
global $wp_filter;

$hooks_to_check = array(
    'plugins_loaded',
    'init',
    'admin_menu',
    'wp_enqueue_scripts',
    'admin_enqueue_scripts'
);

foreach ($hooks_to_check as $hook) {
    if (isset($wp_filter[$hook])) {
        $smartshop_callbacks = 0;
        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function'])) {
                    $class = is_object($callback['function'][0]) ? get_class($callback['function'][0]) : $callback['function'][0];
                    if (strpos($class, 'SmartShop') !== false) {
                        $smartshop_callbacks++;
                    }
                } elseif (is_string($callback['function']) && strpos($callback['function'], 'smartshop') !== false) {
                    $smartshop_callbacks++;
                }
            }
        }
        echo '<p>' . $hook . ': ' . $smartshop_callbacks . ' SmartShop callbacks</p>';
    } else {
        echo '<p>' . $hook . ': No callbacks registered</p>';
    }
}

// Step 8: Memory usage
echo '<h2>Step 8: Memory Usage</h2>';
echo '<p>Current memory usage: ' . size_format(memory_get_usage(true)) . '</p>';
echo '<p>Peak memory usage: ' . size_format(memory_get_peak_usage(true)) . '</p>';
echo '<p>Memory limit: ' . ini_get('memory_limit') . '</p>';

echo '<h2>Conclusion</h2>';
echo '<p>If all steps show ✅, the plugin should activate successfully.</p>';
echo '<p>If any step shows ❌, that indicates the source of the activation error.</p>';
echo '<p><strong>Delete this test-activation.php file after troubleshooting!</strong></p>';
?>
