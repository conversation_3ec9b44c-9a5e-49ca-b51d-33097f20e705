<?php
/**
 * Minimal SmartShop Test
 * 
 * This file tests if the minimal plugin can be activated
 * Upload to WordPress root and access via browser
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('./wp-load.php');

echo '<h1>SmartShop Minimal Activation Test</h1>';

// Test 1: Check if main file exists and can be included
echo '<h2>Test 1: Main File Inclusion</h2>';
$plugin_dir = WP_PLUGIN_DIR . '/smartshop';
$main_file = $plugin_dir . '/smartshop.php';

if (!file_exists($main_file)) {
    echo '<p>❌ Main plugin file not found: ' . $main_file . '</p>';
    exit;
}

try {
    // Capture any output during inclusion
    ob_start();
    include_once $main_file;
    $output = ob_get_clean();
    
    if (!empty($output)) {
        echo '<p>⚠️ Output during inclusion:</p>';
        echo '<pre>' . htmlspecialchars($output) . '</pre>';
    } else {
        echo '<p>✅ Main file included successfully without output</p>';
    }
} catch (Exception $e) {
    echo '<p>❌ Exception during inclusion: ' . $e->getMessage() . '</p>';
    exit;
} catch (ParseError $e) {
    echo '<p>❌ Parse error: ' . $e->getMessage() . '</p>';
    exit;
} catch (Error $e) {
    echo '<p>❌ Fatal error: ' . $e->getMessage() . '</p>';
    exit;
}

// Test 2: Check if main class exists
echo '<h2>Test 2: Main Class Check</h2>';
if (class_exists('SmartShop')) {
    echo '<p>✅ SmartShop class exists</p>';
    
    // Test 3: Try to get instance
    echo '<h2>Test 3: Instance Creation</h2>';
    try {
        $instance = SmartShop::get_instance();
        if ($instance instanceof SmartShop) {
            echo '<p>✅ SmartShop instance created successfully</p>';
        } else {
            echo '<p>❌ Instance is not of SmartShop class</p>';
        }
    } catch (Exception $e) {
        echo '<p>❌ Error creating instance: ' . $e->getMessage() . '</p>';
    }
} else {
    echo '<p>❌ SmartShop class not found</p>';
    exit;
}

// Test 4: Check if plugin is activatable
echo '<h2>Test 4: Activation Test</h2>';
$active_plugins = get_option('active_plugins', array());
$plugin_file = 'smartshop/smartshop.php';

if (in_array($plugin_file, $active_plugins)) {
    echo '<p>✅ Plugin is currently active</p>';
} else {
    echo '<p>⚠️ Plugin is not active</p>';
    
    // Try to simulate activation
    try {
        if (method_exists('SmartShop', 'activate')) {
            $instance = SmartShop::get_instance();
            $instance->activate();
            echo '<p>✅ Activation method executed successfully</p>';
        } else {
            echo '<p>❌ Activation method not found</p>';
        }
    } catch (Exception $e) {
        echo '<p>❌ Error during activation: ' . $e->getMessage() . '</p>';
    }
}

// Test 5: Check WordPress hooks
echo '<h2>Test 5: WordPress Hooks</h2>';
global $wp_filter;

$hooks_to_check = array(
    'plugins_loaded' => 'Plugin initialization',
    'init' => 'WordPress init',
    'admin_menu' => 'Admin menu'
);

foreach ($hooks_to_check as $hook => $description) {
    if (isset($wp_filter[$hook])) {
        $smartshop_callbacks = 0;
        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function'])) {
                    $class = is_object($callback['function'][0]) ? get_class($callback['function'][0]) : $callback['function'][0];
                    if (strpos($class, 'SmartShop') !== false) {
                        $smartshop_callbacks++;
                    }
                }
            }
        }
        
        if ($smartshop_callbacks > 0) {
            echo '<p>✅ ' . $description . ': ' . $smartshop_callbacks . ' callback(s) registered</p>';
        } else {
            echo '<p>⚠️ ' . $description . ': No SmartShop callbacks found</p>';
        }
    } else {
        echo '<p>❌ ' . $description . ': Hook not found</p>';
    }
}

// Test 6: Check plugin options
echo '<h2>Test 6: Plugin Options</h2>';
$version = get_option('smartshop_version');
$activated = get_option('smartshop_activated');

echo '<p>Version option: ' . ($version ? '✅ ' . $version : '❌ Not set') . '</p>';
echo '<p>Activated flag: ' . ($activated ? '✅ Yes' : '❌ No') . '</p>';

// Test 7: Memory usage
echo '<h2>Test 7: Memory Usage</h2>';
echo '<p>Current memory usage: ' . size_format(memory_get_usage(true)) . '</p>';
echo '<p>Peak memory usage: ' . size_format(memory_get_peak_usage(true)) . '</p>';
echo '<p>Memory limit: ' . ini_get('memory_limit') . '</p>';

// Test 8: Error log check
echo '<h2>Test 8: Error Check</h2>';
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    $log_content = file_get_contents($error_log);
    $recent_errors = array();
    $lines = explode("\n", $log_content);
    
    // Get recent errors (last 50 lines)
    $recent_lines = array_slice($lines, -50);
    
    foreach ($recent_lines as $line) {
        if (stripos($line, 'smartshop') !== false || stripos($line, 'fatal') !== false) {
            $recent_errors[] = $line;
        }
    }
    
    if (!empty($recent_errors)) {
        echo '<p>⚠️ Recent errors found:</p>';
        echo '<pre style="background: #f0f0f0; padding: 10px; max-height: 200px; overflow-y: auto;">';
        echo htmlspecialchars(implode("\n", array_slice($recent_errors, -10)));
        echo '</pre>';
    } else {
        echo '<p>✅ No recent SmartShop or fatal errors found</p>';
    }
} else {
    echo '<p>⚠️ Error log not accessible</p>';
}

echo '<h2>Summary</h2>';
echo '<p>If all tests show ✅, the plugin should activate successfully in WordPress admin.</p>';
echo '<p>If any test shows ❌, that indicates an issue that needs to be fixed.</p>';
echo '<p>⚠️ warnings are usually not critical but should be investigated.</p>';

echo '<h3>Next Steps:</h3>';
echo '<ol>';
echo '<li>If all tests pass, try activating the plugin in WordPress admin</li>';
echo '<li>If activation fails, check the error log for specific error messages</li>';
echo '<li>If needed, temporarily rename other eCommerce plugins to avoid conflicts</li>';
echo '<li>Ensure your hosting environment meets the minimum requirements</li>';
echo '</ol>';

echo '<p><strong>Delete this test-minimal.php file after testing!</strong></p>';
?>
